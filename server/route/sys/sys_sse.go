package sys

import (
	"server/core/manager/ssemgr"
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type SSERoute struct{}

// InitSSERoute 初始化SSE路由
func (r *SSERoute) InitSSERoute(Router *gin.RouterGroup) gin.IRoutes {
	sseRouter := Router.Group("/sse").Use(middleware.OperationRecord())
	handler := ssemgr.NewHandler()

	{
		// SSE连接端点
		sseRouter.GET("/notification", handler.HandleNotificationSSE) // 通知SSE
		sseRouter.GET("/dashboard", handler.HandleDashboardSSE)       // 仪表盘SSE
		sseRouter.GET("/log", handler.HandleLogSSE)                   // 日志SSE
		sseRouter.GET("/network", handler.HandleNetworkSSE)           // 网络SSE

		// SSE管理端点
		sseRouter.GET("/stats", handler.GetSSEStats)                      // 获取统计信息
		sseRouter.GET("/connections", handler.GetSSEConnections)          // 获取连接信息
		sseRouter.DELETE("/connection/:connId", handler.CloseSSEConnection) // 关闭指定连接
		sseRouter.DELETE("/user/:userId", handler.CloseUserSSEConnections) // 关闭用户连接

		// 事件订阅管理
		sseRouter.POST("/subscribe", handler.SubscribeEvent)     // 订阅事件
		sseRouter.POST("/unsubscribe", handler.UnsubscribeEvent) // 取消订阅

		// 测试端点
		sseRouter.POST("/test", handler.SendTestMessage) // 发送测试消息
	}

	return sseRouter
}

// InitSSERoute 全局初始化函数
func InitSSERoute(Router *gin.RouterGroup) gin.IRoutes {
	return (&SSERoute{}).InitSSERoute(Router)
}
