package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type CronRoute struct{}

// InitCronRoute 初始化定时任务路由
func (cr *CronRoute) InitCronRoute(Router *gin.RouterGroup) {
	cronRouter := Router.Group("cron").Use(middleware.OperationRecord())
	{
		// 任务管理
		cronRouter.POST("/tasks", cronApi.CreateTask)       // 创建任务
		cronRouter.GET("/tasks", cronApi.GetTaskList)       // 获取任务列表
		cronRouter.GET("/tasks/:id", cronApi.GetTask)       // 获取任务详情
		cronRouter.PUT("/tasks/:id", cronApi.UpdateTask)    // 更新任务
		cronRouter.DELETE("/tasks/:id", cronApi.DeleteTask) // 删除任务

		// 任务控制
		cronRouter.POST("/tasks/:id/start", cronApi.StartTask)     // 启动任务
		cronRouter.POST("/tasks/:id/stop", cronApi.StopTask)       // 停止任务
		cronRouter.POST("/tasks/:id/pause", cronApi.PauseTask)     // 暂停任务
		cronRouter.POST("/tasks/:id/execute", cronApi.ExecuteTask) // 立即执行任务

		// 执行历史
		cronRouter.GET("/executions", cronApi.GetExecutionList) // 获取执行记录列表
		cronRouter.GET("/executions/:id", cronApi.GetExecution) // 获取执行记录详情

		// 任务模板
		cronRouter.GET("/templates", cronApi.GetTemplateList)       // 获取模板列表
		cronRouter.POST("/templates", cronApi.CreateTemplate)       // 创建模板
		cronRouter.GET("/templates/:id", cronApi.GetTemplate)       // 获取模板详情
		cronRouter.PUT("/templates/:id", cronApi.UpdateTemplate)    // 更新模板
		cronRouter.DELETE("/templates/:id", cronApi.DeleteTemplate) // 删除模板

		// 工具接口
		cronRouter.POST("/validate", cronApi.ValidateCronExpr) // 验证Cron表达式
	}
}
