package cron

// CreateCronTaskRequest 创建定时任务请求
type CreateCronTaskRequest struct {
	Name        string                 `json:"name" binding:"required,max=100"`      // 任务名称
	Description string                 `json:"description" binding:"max=500"`        // 任务描述
	ClientID    uint                   `json:"client_id" binding:"required"`         // 目标客户端ID
	TaskType    string                 `json:"task_type" binding:"required"`         // 任务类型
	CronExpr    string                 `json:"cron_expr" binding:"required,max=100"` // Cron表达式
	Command     string                 `json:"command"`                              // 执行命令/脚本内容
	Params      map[string]interface{} `json:"params"`                               // 任务参数
	Timeout     int                    `json:"timeout" binding:"min=1,max=3600"`     // 超时时间(秒)
	RetryCount  int                    `json:"retry_count" binding:"min=0,max=10"`   // 重试次数
}

// UpdateCronTaskRequest 更新定时任务请求
type UpdateCronTaskRequest struct {
	Name        string                 `json:"name" binding:"max=100"`             // 任务名称
	Description string                 `json:"description" binding:"max=500"`      // 任务描述
	CronExpr    string                 `json:"cron_expr" binding:"max=100"`        // Cron表达式
	Command     string                 `json:"command"`                            // 执行命令/脚本内容
	Params      map[string]interface{} `json:"params"`                             // 任务参数
	Timeout     int                    `json:"timeout" binding:"min=1,max=3600"`   // 超时时间(秒)
	RetryCount  int                    `json:"retry_count" binding:"min=0,max=10"` // 重试次数
}

// CronTaskListRequest 定时任务列表请求
type CronTaskListRequest struct {
	Page     int    `json:"page" form:"page"`           // 页码
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量
	ClientID uint   `json:"client_id" form:"client_id"` // 客户端ID筛选
	TaskType string `json:"task_type" form:"task_type"` // 任务类型筛选
	Status   string `json:"status" form:"status"`       // 状态筛选
	Keyword  string `json:"keyword" form:"keyword"`     // 关键词搜索
}

// CronTaskResponse 定时任务响应
type CronTaskResponse struct {
	ID              uint64  `json:"id"`
	Name            string  `json:"name"`
	Description     string  `json:"description"`
	ClientID        uint    `json:"client_id"`
	ClientName      string  `json:"client_name"` // 客户端名称
	TaskType        string  `json:"task_type"`
	TaskTypeDisplay string  `json:"task_type_display"` // 任务类型显示名称
	CronExpr        string  `json:"cron_expr"`
	Command         string  `json:"command"`
	Params          string  `json:"params"`
	Status          string  `json:"status"`
	StatusDisplay   string  `json:"status_display"` // 状态显示名称
	Timeout         int     `json:"timeout"`
	RetryCount      int     `json:"retry_count"`
	CreatedBy       uint    `json:"created_by"`
	CreatedByName   string  `json:"created_by_name"` // 创建者名称
	SourceType      string  `json:"source_type"`     // 来源类型
	SourceID        *uint64 `json:"source_id"`       // 来源ID
	IsManaged       bool    `json:"is_managed"`      // 是否由其他系统管理
	CreatedAt       string  `json:"created_at"`
	UpdatedAt       string  `json:"updated_at"`
	NextRunAt       string  `json:"next_run_at"` // 下次执行时间
	LastRunAt       string  `json:"last_run_at"` // 上次执行时间
}

// CronTaskListResponse 定时任务列表响应
type CronTaskListResponse struct {
	List     []CronTaskResponse `json:"list"`
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
}

// CronExecutionListRequest 执行记录列表请求
type CronExecutionListRequest struct {
	Page     int    `json:"page" form:"page"`           // 页码
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量
	TaskID   uint64 `json:"task_id" form:"task_id"`     // 任务ID筛选
	ClientID uint   `json:"client_id" form:"client_id"` // 客户端ID筛选
	Status   string `json:"status" form:"status"`       // 状态筛选
}

// CronExecutionResponse 执行记录响应
type CronExecutionResponse struct {
	ID            uint64 `json:"id"`
	TaskID        uint64 `json:"task_id"`
	TaskName      string `json:"task_name"` // 任务名称
	ClientID      uint   `json:"client_id"`
	ClientName    string `json:"client_name"` // 客户端名称
	Status        string `json:"status"`
	StatusDisplay string `json:"status_display"` // 状态显示名称
	Output        string `json:"output"`
	ErrorMsg      string `json:"error_msg"`
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
	Duration      int64  `json:"duration"` // 执行时长(毫秒)
	CreatedAt     string `json:"created_at"`
}

// CronExecutionListResponse 执行记录列表响应
type CronExecutionListResponse struct {
	List     []CronExecutionResponse `json:"list"`
	Total    int64                   `json:"total"`
	Page     int                     `json:"page"`
	PageSize int                     `json:"page_size"`
}

// CronTemplateRequest 任务模板请求
type CronTemplateRequest struct {
	Name        string                 `json:"name" binding:"required,max=100"`      // 模板名称
	Description string                 `json:"description" binding:"max=500"`        // 模板描述
	Category    string                 `json:"category" binding:"required,max=50"`   // 模板分类
	TaskType    string                 `json:"task_type" binding:"required,max=50"`  // 任务类型
	CronExpr    string                 `json:"cron_expr" binding:"required,max=100"` // 默认Cron表达式
	Command     string                 `json:"command"`                              // 默认命令/脚本
	Params      map[string]interface{} `json:"params"`                               // 默认参数
	Timeout     int                    `json:"timeout" binding:"min=1,max=3600"`     // 默认超时时间
	RetryCount  int                    `json:"retry_count" binding:"min=0,max=10"`   // 默认重试次数
}

// CronTemplateResponse 任务模板响应
type CronTemplateResponse struct {
	ID              uint64 `json:"id"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	Category        string `json:"category"`
	CategoryDisplay string `json:"category_display"` // 分类显示名称
	TaskType        string `json:"task_type"`
	TaskTypeDisplay string `json:"task_type_display"` // 任务类型显示名称
	CronExpr        string `json:"cron_expr"`
	Command         string `json:"command"`
	Params          string `json:"params"`
	Timeout         int    `json:"timeout"`
	RetryCount      int    `json:"retry_count"`
	IsBuiltIn       bool   `json:"is_built_in"`
	CreatedBy       uint   `json:"created_by"`
	CreatedByName   string `json:"created_by_name"` // 创建者名称
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}

// CronTemplateListResponse 任务模板列表响应
type CronTemplateListResponse struct {
	List []CronTemplateResponse `json:"list"`
}

// TaskControlRequest 任务控制请求
type TaskControlRequest struct {
	Action string `json:"action" binding:"required,oneof=start pause stop"` // 操作类型: start, pause, stop
}

// CronTaskStatsResponse 定时任务统计响应
type CronTaskStatsResponse struct {
	TotalTasks    int64 `json:"total_tasks"`    // 总任务数
	ActiveTasks   int64 `json:"active_tasks"`   // 活跃任务数
	PausedTasks   int64 `json:"paused_tasks"`   // 暂停任务数
	StoppedTasks  int64 `json:"stopped_tasks"`  // 停止任务数
	TotalClients  int64 `json:"total_clients"`  // 总客户端数
	OnlineClients int64 `json:"online_clients"` // 在线客户端数
}

// CronExecutionStatsResponse 执行统计响应
type CronExecutionStatsResponse struct {
	TotalExecutions   int64   `json:"total_executions"`   // 总执行次数
	SuccessExecutions int64   `json:"success_executions"` // 成功执行次数
	FailedExecutions  int64   `json:"failed_executions"`  // 失败执行次数
	SuccessRate       float64 `json:"success_rate"`       // 成功率
	AvgDuration       float64 `json:"avg_duration"`       // 平均执行时长(毫秒)
}

// ValidateCronExprRequest 验证Cron表达式请求
type ValidateCronExprRequest struct {
	CronExpr string `json:"cron_expr" binding:"required"` // Cron表达式
}

// ValidateCronExprResponse 验证Cron表达式响应
type ValidateCronExprResponse struct {
	Valid       bool     `json:"valid"`       // 是否有效
	Error       string   `json:"error"`       // 错误信息
	Description string   `json:"description"` // 表达式描述
	NextRuns    []string `json:"next_runs"`   // 接下来几次执行时间
}

// ExecuteTaskRequest 立即执行任务请求
type ExecuteTaskRequest struct {
	TaskID uint64 `json:"task_id" binding:"required"` // 任务ID
}
