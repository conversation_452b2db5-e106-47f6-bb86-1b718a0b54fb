package cron

import (
	"context"
	"fmt"
	"server/global"
	"server/model/task"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// CronManager 定时任务管理器
type CronManager struct {
	cron      *cron.Cron                // cron调度器
	tasks     map[uint64]*task.CronTask // 内存中的任务映射
	entries   map[uint64]cron.EntryID   // 任务ID到cron条目ID的映射
	executors map[string]TaskExecutor   // 任务执行器映射
	mutex     sync.RWMutex              // 并发安全
	ctx       context.Context           // 上下文
	cancel    context.CancelFunc        // 取消函数
}

// TaskExecutor 任务执行器接口
type TaskExecutor interface {
	Execute(ctx context.Context, task *task.CronTask) (*ExecutionResult, error)
	Validate(task *task.CronTask) error
	GetType() string
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	Success   bool      `json:"success"`
	Output    string    `json:"output"`
	Error     string    `json:"error,omitempty"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Duration  int64     `json:"duration"` // 毫秒
}

// 全局Cron管理器实例
var GlobalCronManager *CronManager

// InitCronManager 初始化Cron管理器
func InitCronManager() error {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &CronManager{
		cron:      cron.New(cron.WithSeconds()), // 支持秒级精度
		tasks:     make(map[uint64]*task.CronTask),
		entries:   make(map[uint64]cron.EntryID),
		executors: make(map[string]TaskExecutor),
		ctx:       ctx,
		cancel:    cancel,
	}

	// 注册默认执行器
	manager.RegisterExecutor(&CommandExecutor{})
	manager.RegisterExecutor(&ScreenshotExecutor{})
	manager.RegisterExecutor(&ScriptExecutor{})

	// 启动cron调度器
	manager.cron.Start()

	// 从数据库加载活跃任务
	if err := manager.LoadActiveTasks(); err != nil {
		global.LOG.Error("加载活跃任务失败", zap.Error(err))
		return err
	}

	GlobalCronManager = manager
	global.CronManager = manager // 设置全局接口
	global.LOG.Info("Cron管理器初始化成功")
	return nil
}

// RegisterExecutor 注册任务执行器
func (cm *CronManager) RegisterExecutor(executor TaskExecutor) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.executors[executor.GetType()] = executor
	global.LOG.Info("注册任务执行器", zap.String("type", executor.GetType()))
}

// AddTask 添加定时任务
func (cm *CronManager) AddTask(cronTask *task.CronTask) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 验证任务配置
	executor, exists := cm.executors[cronTask.TaskType]
	if !exists {
		return fmt.Errorf("不支持的任务类型: %s", cronTask.TaskType)
	}

	if err := executor.Validate(cronTask); err != nil {
		return fmt.Errorf("任务配置验证失败: %w", err)
	}

	// 添加到cron调度器
	entryID, err := cm.cron.AddFunc(cronTask.CronExpr, func() {
		cm.executeTask(cronTask)
	})
	if err != nil {
		return fmt.Errorf("添加cron任务失败: %w", err)
	}

	// 更新内存映射
	cm.tasks[cronTask.ID] = cronTask
	cm.entries[cronTask.ID] = entryID

	// 计算下次执行时间
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	schedule, err := parser.Parse(cronTask.CronExpr)
	if err == nil {
		nextRun := schedule.Next(time.Now())
		cronTask.NextRunAt = &nextRun
	}

	global.LOG.Info("添加定时任务成功",
		zap.Uint64("taskID", cronTask.ID),
		zap.String("name", cronTask.Name),
		zap.String("cron", cronTask.CronExpr))

	return nil
}

// RemoveTask 移除定时任务
func (cm *CronManager) RemoveTask(taskID uint64) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 从cron调度器中移除
	if entryID, exists := cm.entries[taskID]; exists {
		cm.cron.Remove(entryID)
		delete(cm.entries, taskID)
	}

	// 从内存中移除
	delete(cm.tasks, taskID)

	global.LOG.Info("移除定时任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// UpdateTask 更新定时任务
func (cm *CronManager) UpdateTask(cronTask *task.CronTask) error {
	// 先移除旧任务
	if err := cm.RemoveTask(cronTask.ID); err != nil {
		return err
	}

	// 如果任务是活跃状态，重新添加
	if cronTask.Status == "active" {
		return cm.AddTask(cronTask)
	}

	return nil
}

// executeTask 执行任务
func (cm *CronManager) executeTask(cronTask *task.CronTask) {
	// 检查任务状态
	if cronTask.Status != "active" {
		return
	}

	// 获取执行器
	executor, exists := cm.executors[cronTask.TaskType]
	if !exists {
		global.LOG.Error("找不到任务执行器",
			zap.Uint64("taskID", cronTask.ID),
			zap.String("type", cronTask.TaskType))
		return
	}

	// 创建执行上下文
	ctx := cm.ctx
	if cronTask.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(cm.ctx, time.Duration(cronTask.Timeout)*time.Second)
		defer cancel()
	}

	// 执行任务
	startTime := time.Now()
	result, err := executor.Execute(ctx, cronTask)

	// 记录执行结果
	execution := &task.CronExecution{
		TaskID:    cronTask.ID,
		ClientID:  cronTask.ClientID,
		StartTime: startTime,
		EndTime:   time.Now(),
		Duration:  time.Since(startTime).Milliseconds(),
	}

	if err != nil {
		execution.Status = "failed"
		execution.ErrorMsg = err.Error()
		global.LOG.Error("任务执行失败",
			zap.Uint64("taskID", cronTask.ID),
			zap.Error(err))
	} else if result != nil {
		if result.Success {
			execution.Status = "success"
		} else {
			execution.Status = "failed"
			execution.ErrorMsg = result.Error
		}
		execution.Output = result.Output
		execution.Duration = result.Duration
	}

	// 保存执行记录到数据库
	if err := global.DB.Create(execution).Error; err != nil {
		global.LOG.Error("保存执行记录失败", zap.Error(err))
	}

	// 更新下次执行时间
	cm.updateNextRunTime(cronTask)

	global.LOG.Info("任务执行完成",
		zap.Uint64("taskID", cronTask.ID),
		zap.String("status", execution.Status),
		zap.Int64("duration", execution.Duration))
}

// updateNextRunTime 更新下次执行时间
func (cm *CronManager) updateNextRunTime(cronTask *task.CronTask) {
	schedule, err := cron.ParseStandard(cronTask.CronExpr)
	if err != nil {
		return
	}

	nextRun := schedule.Next(time.Now())
	cronTask.NextRunAt = &nextRun

	// 更新数据库
	global.DB.Model(cronTask).Update("next_run_at", nextRun)
}

// LoadActiveTasks 从数据库加载活跃任务
func (cm *CronManager) LoadActiveTasks() error {
	var tasks []task.CronTask
	if err := global.DB.Where("status = ?", "active").Find(&tasks).Error; err != nil {
		return err
	}

	for _, cronTask := range tasks {
		if err := cm.AddTask(&cronTask); err != nil {
			global.LOG.Error("加载任务失败",
				zap.Uint64("taskID", cronTask.ID),
				zap.Error(err))
		}
	}

	global.LOG.Info("加载活跃任务完成", zap.Int("count", len(tasks)))
	return nil
}

// GetTaskStatus 获取任务状态
func (cm *CronManager) GetTaskStatus(taskID uint64) (*task.CronTask, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	cronTask, exists := cm.tasks[taskID]
	return cronTask, exists
}

// GetAllTasks 获取所有内存中的任务
func (cm *CronManager) GetAllTasks() map[uint64]*task.CronTask {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[uint64]*task.CronTask)
	for id, cronTask := range cm.tasks {
		result[id] = cronTask
	}

	return result
}

// Shutdown 关闭Cron管理器
func (cm *CronManager) Shutdown() {
	if cm.cancel != nil {
		cm.cancel()
	}

	if cm.cron != nil {
		cm.cron.Stop()
	}

	global.LOG.Info("Cron管理器已关闭")
}

// 实现CronManagerInterface接口的适配方法

// AddCronTask 添加任务（接口适配）
func (cm *CronManager) AddCronTask(cronTaskInterface interface{}) error {
	cronTask, ok := cronTaskInterface.(*task.CronTask)
	if !ok {
		return fmt.Errorf("无效的任务类型")
	}
	return cm.AddTask(cronTask)
}

// RemoveCronTask 移除任务（接口适配）
func (cm *CronManager) RemoveCronTask(taskID uint64) error {
	return cm.RemoveTask(taskID)
}

// UpdateCronTask 更新任务（接口适配）
func (cm *CronManager) UpdateCronTask(cronTaskInterface interface{}) error {
	cronTask, ok := cronTaskInterface.(*task.CronTask)
	if !ok {
		return fmt.Errorf("无效的任务类型")
	}
	return cm.UpdateTask(cronTask)
}

// ExecuteTaskNow 立即执行任务
func (cm *CronManager) ExecuteTaskNow(taskID uint64) error {
	cm.mutex.RLock()
	cronTask, exists := cm.tasks[taskID]
	cm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("任务不存在")
	}

	// 在新的goroutine中执行任务，避免阻塞
	go cm.executeTask(cronTask)

	global.LOG.Info("立即执行任务", zap.Uint64("taskID", taskID))
	return nil
}
