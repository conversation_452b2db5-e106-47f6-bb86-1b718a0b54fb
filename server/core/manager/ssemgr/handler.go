package ssemgr

import (
	"fmt"
	"net/http"
	"strconv"

	"server/global"
	"server/model/response"
	"server/model/sys"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Handler SSE处理器
type Handler struct{}

// NewHandler 创建新的SSE处理器
func NewHandler() *Handler {
	return &Handler{}
}

// HandleSSE 处理SSE连接请求
func (h *Handler) HandleSSE(c *gin.Context, connType ConnectionType) {
	// 验证token
	token := sys.GetToken(c)
	if token == "" {
		c.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	claims, err := j.<PERSON>oken(token)
	if err != nil {
		global.LOG.Error("SSE连接认证失败",
			zap.String("type", string(connType)),
			zap.Error(err))
		c.String(http.StatusUnauthorized, "认证失败")
		return
	}

	userID := claims.BaseClaims.ID

	// 检查管理器是否可用
	if GlobalManager == nil {
		global.LOG.Error("SSE管理器未初始化")
		c.String(http.StatusInternalServerError, "SSE服务不可用")
		return
	}

	// 添加连接
	conn, err := GlobalManager.AddConnection(userID, connType, c.Writer, c.Request)
	if err != nil {
		global.LOG.Error("添加SSE连接失败",
			zap.Uint("userID", userID),
			zap.String("type", string(connType)),
			zap.Error(err))
		c.String(http.StatusInternalServerError, "创建SSE连接失败: "+err.Error())
		return
	}

	global.LOG.Info("SSE连接已建立",
		zap.String("connID", conn.ID),
		zap.Uint("userID", userID),
		zap.String("type", string(connType)),
		zap.String("remoteAddr", c.Request.RemoteAddr))

	// 处理连接
	GlobalManager.HandleConnection(conn)
}

// HandleNotificationSSE 处理通知SSE连接
func (h *Handler) HandleNotificationSSE(c *gin.Context) {
	h.HandleSSE(c, TypeNotification)
}

// HandleDashboardSSE 处理仪表盘SSE连接
func (h *Handler) HandleDashboardSSE(c *gin.Context) {
	h.HandleSSE(c, TypeDashboard)
}

// HandleLogSSE 处理日志SSE连接
func (h *Handler) HandleLogSSE(c *gin.Context) {
	h.HandleSSE(c, TypeLog)
}

// HandleNetworkSSE 处理网络SSE连接
func (h *Handler) HandleNetworkSSE(c *gin.Context) {
	h.HandleSSE(c, TypeNetwork)
}

// GetSSEStats 获取SSE统计信息
func (h *Handler) GetSSEStats(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	stats := GlobalManager.GetStats()
	response.OkWithDetailed(stats, "获取成功", c)
}

// GetSSEConnections 获取SSE连接信息
func (h *Handler) GetSSEConnections(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	// 获取查询参数
	userIDStr := c.Query("user_id")
	connType := c.Query("type")

	var connections []*Connection

	if userIDStr != "" {
		// 获取指定用户的连接
		userID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			response.ErrorWithMessage("用户ID参数错误", c)
			return
		}
		connections = GlobalManager.GetConnectionsByUser(uint(userID))
	} else if connType != "" {
		// 获取指定类型的连接
		connections = GlobalManager.GetConnectionsByType(ConnectionType(connType))
	} else {
		// 获取所有连接
		allConns := GlobalManager.GetConnections()
		connections = make([]*Connection, 0, len(allConns))
		for _, conn := range allConns {
			connections = append(connections, conn)
		}
	}

	// 转换为响应格式
	var result []map[string]interface{}
	for _, conn := range connections {
		result = append(result, map[string]interface{}{
			"id":            conn.ID,
			"user_id":       conn.UserID,
			"type":          conn.Type,
			"remote_addr":   conn.RemoteAddr,
			"created_at":    conn.CreatedAt,
			"last_ping":     conn.LastPing,
			"subscriptions": conn.Subscriptions,
		})
	}

	response.OkWithDetailed(gin.H{
		"connections": result,
		"total":       len(result),
	}, "获取成功", c)
}

// CloseSSEConnection 关闭指定的SSE连接
func (h *Handler) CloseSSEConnection(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	connID := c.Param("connId")
	if connID == "" {
		response.ErrorWithMessage("连接ID不能为空", c)
		return
	}

	GlobalManager.RemoveConnection(connID)
	response.OkWithMessage("连接已关闭", c)
}

// CloseUserSSEConnections 关闭用户的所有SSE连接
func (h *Handler) CloseUserSSEConnections(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("用户ID参数错误", c)
		return
	}

	connections := GlobalManager.GetConnectionsByUser(uint(userID))
	for _, conn := range connections {
		GlobalManager.RemoveConnection(conn.ID)
	}

	response.OkWithMessage(fmt.Sprintf("已关闭用户 %d 的 %d 个连接", userID, len(connections)), c)
}

// SubscribeEvent 订阅事件
func (h *Handler) SubscribeEvent(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	var req struct {
		ConnID    string `json:"conn_id" binding:"required"`
		EventType string `json:"event_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := GlobalManager.Subscribe(req.ConnID, req.EventType); err != nil {
		response.ErrorWithMessage("订阅失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("订阅成功", c)
}

// UnsubscribeEvent 取消订阅事件
func (h *Handler) UnsubscribeEvent(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	var req struct {
		ConnID    string `json:"conn_id" binding:"required"`
		EventType string `json:"event_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := GlobalManager.Unsubscribe(req.ConnID, req.EventType); err != nil {
		response.ErrorWithMessage("取消订阅失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("取消订阅成功", c)
}

// SendTestMessage 发送测试消息
func (h *Handler) SendTestMessage(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	var req struct {
		UserID    uint           `json:"user_id"`
		Type      ConnectionType `json:"type"`
		Event     string         `json:"event" binding:"required"`
		Data      interface{}    `json:"data"`
		Broadcast bool           `json:"broadcast"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	var err error
	if req.Broadcast {
		if req.Type != "" {
			err = GlobalManager.BroadcastToType(req.Type, req.Event, req.Data)
		} else {
			err = GlobalManager.BroadcastToAll(req.Event, req.Data)
		}
	} else if req.UserID > 0 {
		if req.Type != "" {
			err = GlobalManager.SendToUserByType(req.UserID, req.Type, req.Event, req.Data)
		} else {
			err = GlobalManager.SendToUser(req.UserID, req.Event, req.Data)
		}
	} else {
		response.ErrorWithMessage("必须指定用户ID或设置为广播", c)
		return
	}

	if err != nil {
		response.ErrorWithMessage("发送失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("发送成功", c)
}
