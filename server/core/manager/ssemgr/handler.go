package ssemgr

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"server/global"
	"server/model/response"
	"server/model/sys"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Handler SSE处理器
type Handler struct{}

// NewHandler 创建新的SSE处理器
func NewHandler() *Handler {
	return &Handler{}
}

// HandleSSE 处理SSE连接请求
func (h *Handler) HandleSSE(c *gin.Context, connType ConnectionType) {
	// 验证token
	token := sys.GetToken(c)
	if token == "" {
		c.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	claims, err := j.Parse<PERSON>oken(token)
	if err != nil {
		global.LOG.Error("SSE连接认证失败",
			zap.String("type", string(connType)),
			zap.Error(err))
		c.String(http.StatusUnauthorized, "认证失败")
		return
	}

	userID := claims.BaseClaims.ID

	// 检查管理器是否可用
	if GlobalManager == nil {
		global.LOG.Error("SSE管理器未初始化")
		c.String(http.StatusInternalServerError, "SSE服务不可用")
		return
	}

	// 添加连接
	conn, err := GlobalManager.AddConnection(userID, connType, c.Writer, c.Request)
	if err != nil {
		global.LOG.Error("添加SSE连接失败",
			zap.Uint("userID", userID),
			zap.String("type", string(connType)),
			zap.Error(err))
		c.String(http.StatusInternalServerError, "创建SSE连接失败: "+err.Error())
		return
	}

	global.LOG.Info("SSE连接已建立",
		zap.String("connID", conn.ID),
		zap.Uint("userID", userID),
		zap.String("type", string(connType)),
		zap.String("remoteAddr", c.Request.RemoteAddr))

	// 处理连接
	GlobalManager.HandleConnection(conn)
}

// HandleNotificationSSE 处理通知SSE连接
func (h *Handler) HandleNotificationSSE(c *gin.Context) {
	h.HandleSSE(c, TypeNotification)
}

// HandleDashboardSSE 处理仪表盘SSE连接
func (h *Handler) HandleDashboardSSE(c *gin.Context) {
	h.HandleSSE(c, TypeDashboard)
}

// HandleLogSSE 处理日志SSE连接
func (h *Handler) HandleLogSSE(c *gin.Context) {
	// 获取日志文件路径参数
	filePath := c.Query("path")
	if filePath == "" {
		c.String(http.StatusBadRequest, "文件路径不能为空")
		return
	}

	// 验证token
	token := sys.GetToken(c)
	if token == "" {
		c.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		global.LOG.Error("日志SSE连接认证失败", zap.Error(err))
		c.String(http.StatusUnauthorized, "认证失败")
		return
	}

	userID := claims.BaseClaims.ID

	// 安全检查：确保文件在日志目录内
	logDir := global.CONFIG.Zap.Director
	if logDir == "" {
		logDir = "log"
	}

	absLogDir, err := filepath.Abs(logDir)
	if err != nil {
		c.String(http.StatusBadRequest, "日志目录路径错误")
		return
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		c.String(http.StatusBadRequest, "文件路径错误")
		return
	}

	if !strings.HasPrefix(absFilePath, absLogDir) {
		c.String(http.StatusForbidden, "无权访问该文件")
		return
	}

	// 检查管理器是否可用
	if GlobalManager == nil {
		global.LOG.Error("SSE管理器未初始化")
		c.String(http.StatusInternalServerError, "SSE服务不可用")
		return
	}

	// 添加连接
	conn, err := GlobalManager.AddConnection(userID, TypeLog, c.Writer, c.Request)
	if err != nil {
		global.LOG.Error("添加日志SSE连接失败",
			zap.Uint("userID", userID),
			zap.Error(err))
		c.String(http.StatusInternalServerError, "创建SSE连接失败: "+err.Error())
		return
	}

	global.LOG.Info("日志SSE连接已建立",
		zap.String("connID", conn.ID),
		zap.Uint("userID", userID),
		zap.String("filePath", filePath),
		zap.String("remoteAddr", c.Request.RemoteAddr))

	// 启动日志文件监控
	go h.monitorLogFile(conn, filePath)

	// 处理连接
	GlobalManager.HandleConnection(conn)
}

// HandleNetworkSSE 处理网络SSE连接
func (h *Handler) HandleNetworkSSE(c *gin.Context) {
	// 获取客户端ID参数
	clientIDStr := c.Param("clientId")
	if clientIDStr == "" {
		clientIDStr = c.Query("clientId")
	}

	if clientIDStr == "" {
		c.String(http.StatusBadRequest, "客户端ID不能为空")
		return
	}

	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		c.String(http.StatusBadRequest, "客户端ID参数错误")
		return
	}

	// 验证token
	token := sys.GetToken(c)
	if token == "" {
		c.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		global.LOG.Error("网络SSE连接认证失败", zap.Error(err))
		c.String(http.StatusUnauthorized, "认证失败")
		return
	}

	userID := claims.BaseClaims.ID

	// 检查管理器是否可用
	if GlobalManager == nil {
		global.LOG.Error("SSE管理器未初始化")
		c.String(http.StatusInternalServerError, "SSE服务不可用")
		return
	}

	// 添加连接
	conn, err := GlobalManager.AddConnection(userID, TypeNetwork, c.Writer, c.Request)
	if err != nil {
		global.LOG.Error("添加网络SSE连接失败",
			zap.Uint("userID", userID),
			zap.Uint("clientID", uint(clientID)),
			zap.Error(err))
		c.String(http.StatusInternalServerError, "创建SSE连接失败: "+err.Error())
		return
	}

	global.LOG.Info("网络SSE连接已建立",
		zap.String("connID", conn.ID),
		zap.Uint("userID", userID),
		zap.Uint("clientID", uint(clientID)),
		zap.String("remoteAddr", c.Request.RemoteAddr))

	// 发送初始连接确认
	initialData := map[string]interface{}{
		"type":      "connected",
		"message":   "网络接口进度推送已连接",
		"clientId":  clientID,
		"timestamp": time.Now().Unix(),
	}

	if err := GlobalManager.SendToConnection(conn.ID, "connected", initialData); err != nil {
		global.LOG.Error("发送网络SSE初始消息失败", zap.Error(err))
	}

	// 启动保活循环
	go h.startNetworkKeepAlive(conn, uint(clientID))

	// 处理连接
	GlobalManager.HandleConnection(conn)
}

// GetSSEStats 获取SSE统计信息
func (h *Handler) GetSSEStats(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	stats := GlobalManager.GetStats()
	response.OkWithDetailed(stats, "获取成功", c)
}

// GetSSEConnections 获取SSE连接信息
func (h *Handler) GetSSEConnections(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	// 获取查询参数
	userIDStr := c.Query("user_id")
	connType := c.Query("type")

	var connections []*Connection

	if userIDStr != "" {
		// 获取指定用户的连接
		userID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			response.ErrorWithMessage("用户ID参数错误", c)
			return
		}
		connections = GlobalManager.GetConnectionsByUser(uint(userID))
	} else if connType != "" {
		// 获取指定类型的连接
		connections = GlobalManager.GetConnectionsByType(ConnectionType(connType))
	} else {
		// 获取所有连接
		allConns := GlobalManager.GetConnections()
		connections = make([]*Connection, 0, len(allConns))
		for _, conn := range allConns {
			connections = append(connections, conn)
		}
	}

	// 转换为响应格式
	var result []map[string]interface{}
	for _, conn := range connections {
		result = append(result, map[string]interface{}{
			"id":            conn.ID,
			"user_id":       conn.UserID,
			"type":          conn.Type,
			"remote_addr":   conn.RemoteAddr,
			"created_at":    conn.CreatedAt,
			"last_ping":     conn.LastPing,
			"subscriptions": conn.Subscriptions,
		})
	}

	response.OkWithDetailed(gin.H{
		"connections": result,
		"total":       len(result),
	}, "获取成功", c)
}

// CloseSSEConnection 关闭指定的SSE连接
func (h *Handler) CloseSSEConnection(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	connID := c.Param("connId")
	if connID == "" {
		response.ErrorWithMessage("连接ID不能为空", c)
		return
	}

	GlobalManager.RemoveConnection(connID)
	response.OkWithMessage("连接已关闭", c)
}

// CloseUserSSEConnections 关闭用户的所有SSE连接
func (h *Handler) CloseUserSSEConnections(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("用户ID参数错误", c)
		return
	}

	connections := GlobalManager.GetConnectionsByUser(uint(userID))
	for _, conn := range connections {
		GlobalManager.RemoveConnection(conn.ID)
	}

	response.OkWithMessage(fmt.Sprintf("已关闭用户 %d 的 %d 个连接", userID, len(connections)), c)
}

// SubscribeEvent 订阅事件
func (h *Handler) SubscribeEvent(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	var req struct {
		ConnID    string `json:"conn_id" binding:"required"`
		EventType string `json:"event_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := GlobalManager.Subscribe(req.ConnID, req.EventType); err != nil {
		response.ErrorWithMessage("订阅失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("订阅成功", c)
}

// UnsubscribeEvent 取消订阅事件
func (h *Handler) UnsubscribeEvent(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	var req struct {
		ConnID    string `json:"conn_id" binding:"required"`
		EventType string `json:"event_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if err := GlobalManager.Unsubscribe(req.ConnID, req.EventType); err != nil {
		response.ErrorWithMessage("取消订阅失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("取消订阅成功", c)
}

// SendTestMessage 发送测试消息
func (h *Handler) SendTestMessage(c *gin.Context) {
	if GlobalManager == nil {
		response.ErrorWithMessage("SSE服务不可用", c)
		return
	}

	var req struct {
		UserID    uint           `json:"user_id"`
		Type      ConnectionType `json:"type"`
		Event     string         `json:"event" binding:"required"`
		Data      interface{}    `json:"data"`
		Broadcast bool           `json:"broadcast"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	var err error
	if req.Broadcast {
		if req.Type != "" {
			err = GlobalManager.BroadcastToType(req.Type, req.Event, req.Data)
		} else {
			err = GlobalManager.BroadcastToAll(req.Event, req.Data)
		}
	} else if req.UserID > 0 {
		if req.Type != "" {
			err = GlobalManager.SendToUserByType(req.UserID, req.Type, req.Event, req.Data)
		} else {
			err = GlobalManager.SendToUser(req.UserID, req.Event, req.Data)
		}
	} else {
		response.ErrorWithMessage("必须指定用户ID或设置为广播", c)
		return
	}

	if err != nil {
		response.ErrorWithMessage("发送失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("发送成功", c)
}

// monitorLogFile 监控日志文件变化
func (h *Handler) monitorLogFile(conn *Connection, filePath string) {
	// 获取文件当前大小
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		global.LOG.Error("获取日志文件信息失败",
			zap.String("filePath", filePath),
			zap.Error(err))
		return
	}

	lastSize := fileInfo.Size()

	// 发送初始内容（最后100行）
	h.sendInitialLogContent(conn, filePath)

	// 定时检查文件变化
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-conn.Context.Done():
			global.LOG.Info("日志监控停止，连接已关闭",
				zap.String("connID", conn.ID),
				zap.String("filePath", filePath))
			return
		case <-ticker.C:
			currentInfo, err := os.Stat(filePath)
			if err != nil {
				continue
			}

			currentSize := currentInfo.Size()
			if currentSize > lastSize {
				// 文件有新内容
				h.sendNewLogContent(conn, filePath, lastSize, currentSize-lastSize)
				lastSize = currentSize
			}
		}
	}
}

// sendInitialLogContent 发送初始日志内容
func (h *Handler) sendInitialLogContent(conn *Connection, filePath string) {
	file, err := os.Open(filePath)
	if err != nil {
		global.LOG.Error("打开日志文件失败",
			zap.String("filePath", filePath),
			zap.Error(err))
		return
	}
	defer file.Close()

	// 获取文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		return
	}

	// 如果文件很大，只读取最后的部分
	const maxInitialBytes = 50 * 1024 // 50KB
	fileSize := fileInfo.Size()

	var startPos int64 = 0
	if fileSize > maxInitialBytes {
		startPos = fileSize - maxInitialBytes
	}

	// 定位到开始位置
	if startPos > 0 {
		_, err = file.Seek(startPos, io.SeekStart)
		if err != nil {
			return
		}
	}

	// 读取内容
	buffer := make([]byte, fileSize-startPos)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return
	}

	if n > 0 {
		content := string(buffer[:n])
		lines := strings.Split(content, "\n")

		// 发送每一行
		for _, line := range lines {
			if strings.TrimSpace(line) != "" {
				data := map[string]interface{}{
					"line":      line,
					"timestamp": time.Now().Unix(),
					"type":      "initial",
				}

				if err := GlobalManager.SendToConnection(conn.ID, "log", data); err != nil {
					global.LOG.Debug("发送初始日志内容失败",
						zap.String("connID", conn.ID),
						zap.Error(err))
					return
				}
			}
		}
	}
}

// sendNewLogContent 发送新的日志内容
func (h *Handler) sendNewLogContent(conn *Connection, filePath string, offset, length int64) {
	file, err := os.Open(filePath)
	if err != nil {
		return
	}
	defer file.Close()

	// 定位到指定位置
	if offset > 0 {
		_, err = file.Seek(offset, io.SeekStart)
		if err != nil {
			return
		}
	}

	// 读取指定长度的内容
	buffer := make([]byte, length)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return
	}

	if n > 0 {
		content := string(buffer[:n])
		lines := strings.Split(content, "\n")

		for _, line := range lines {
			if strings.TrimSpace(line) != "" {
				data := map[string]interface{}{
					"line":      line,
					"timestamp": time.Now().Unix(),
					"type":      "new",
				}

				if err := GlobalManager.SendToConnection(conn.ID, "log", data); err != nil {
					global.LOG.Debug("发送新日志内容失败",
						zap.String("connID", conn.ID),
						zap.Error(err))
					return
				}
			}
		}
	}
}

// startNetworkKeepAlive 启动网络连接保活
func (h *Handler) startNetworkKeepAlive(conn *Connection, clientID uint) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒发送保活信号
	defer ticker.Stop()

	for {
		select {
		case <-conn.Context.Done():
			global.LOG.Info("网络保活停止，连接已关闭",
				zap.String("connID", conn.ID),
				zap.Uint("clientID", clientID))
			return
		case <-ticker.C:
			// 发送保活信号
			keepAliveData := map[string]interface{}{
				"type":      "keepalive",
				"clientId":  clientID,
				"timestamp": time.Now().Unix(),
			}

			if err := GlobalManager.SendToConnection(conn.ID, "keepalive", keepAliveData); err != nil {
				global.LOG.Debug("发送网络保活信号失败",
					zap.String("connID", conn.ID),
					zap.Uint("clientID", clientID),
					zap.Error(err))
				return
			}
		}
	}
}
