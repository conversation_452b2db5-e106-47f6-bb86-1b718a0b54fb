package ssemgr

import (
	"fmt"
	"net/http"

	"server/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 全局SSE管理器实例
var GlobalManager *Manager

// InitManager 初始化全局SSE管理器
func InitManager() {
	config := DefaultConfig()

	// 可以从配置文件读取配置
	// 这里可以添加从配置文件读取SSE配置的逻辑
	// 例如：config.MaxConnections = global.CONFIG.SSE.MaxConnections

	GlobalManager = NewManager(config)
	GlobalManager.Start()

	global.LOG.Info("全局SSE管理器初始化完成",
		zap.Int("maxConnections", config.MaxConnections),
		zap.Int("maxConnectionsPerUser", config.MaxConnectionsPerUser),
		zap.Duration("pingInterval", config.PingInterval),
		zap.Duration("connectionTimeout", config.ConnectionTimeout))
}

// StopManager 停止全局SSE管理器
func StopManager() {
	if GlobalManager != nil {
		GlobalManager.Stop()
		GlobalManager = nil
		global.LOG.Info("全局SSE管理器已停止")
	}
}

// GetManager 获取全局SSE管理器
func GetManager() *Manager {
	return GlobalManager
}

// 便捷方法 - 直接使用全局管理器

// AddConnection 添加连接到全局管理器
func AddConnection(userID uint, connType ConnectionType, w gin.ResponseWriter, r *http.Request) (*Connection, error) {
	if GlobalManager == nil {
		return nil, fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.AddConnection(userID, connType, w, r)
}

// SendToUser 发送消息到用户
func SendToUser(userID uint, event string, data interface{}) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.SendToUser(userID, event, data)
}

// SendToUserByType 发送消息到用户指定类型的连接
func SendToUserByType(userID uint, connType ConnectionType, event string, data interface{}) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.SendToUserByType(userID, connType, event, data)
}

// BroadcastToType 广播消息到指定类型的所有连接
func BroadcastToType(connType ConnectionType, event string, data interface{}) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.BroadcastToType(connType, event, data)
}

// BroadcastToAll 广播消息到所有连接
func BroadcastToAll(event string, data interface{}) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.BroadcastToAll(event, data)
}

// Subscribe 订阅事件
func Subscribe(connID string, eventType string) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.Subscribe(connID, eventType)
}

// Unsubscribe 取消订阅事件
func Unsubscribe(connID string, eventType string) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.Unsubscribe(connID, eventType)
}

// SendToSubscribers 发送消息到订阅者
func SendToSubscribers(eventType string, event string, data interface{}) error {
	if GlobalManager == nil {
		return fmt.Errorf("SSE管理器未初始化")
	}
	return GlobalManager.SendToSubscribers(eventType, event, data)
}

// GetStats 获取统计信息
func GetStats() *Stats {
	if GlobalManager == nil {
		return &Stats{
			ConnectionsByType: make(map[ConnectionType]int64),
			ConnectionsByUser: make(map[uint]int64),
		}
	}
	return GlobalManager.GetStats()
}

// GetConnections 获取连接信息
func GetConnections() map[string]*Connection {
	if GlobalManager == nil {
		return make(map[string]*Connection)
	}
	return GlobalManager.GetConnections()
}

// GetConnectionsByUser 获取用户的连接
func GetConnectionsByUser(userID uint) []*Connection {
	if GlobalManager == nil {
		return []*Connection{}
	}
	return GlobalManager.GetConnectionsByUser(userID)
}

// GetConnectionsByType 获取指定类型的连接
func GetConnectionsByType(connType ConnectionType) []*Connection {
	if GlobalManager == nil {
		return []*Connection{}
	}
	return GlobalManager.GetConnectionsByType(connType)
}
