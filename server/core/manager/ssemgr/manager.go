package ssemgr

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"server/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ConnectionType 连接类型
type ConnectionType string

const (
	TypeNotification ConnectionType = "notification"
	TypeDashboard    ConnectionType = "dashboard"
	TypeLog          ConnectionType = "log"
	TypeNetwork      ConnectionType = "network"
)

// Connection SSE连接
type Connection struct {
	ID            string         // 连接唯一ID
	UserID        uint           // 用户ID
	Type          ConnectionType // 连接类型
	Writer        gin.ResponseWriter
	Request       *http.Request
	Context       context.Context
	Cancel        context.CancelFunc
	Channel       chan Message    // 消息通道
	LastPing      time.Time       // 最后心跳时间
	CreatedAt     time.Time       // 创建时间
	RemoteAddr    string          // 客户端地址
	Subscriptions map[string]bool // 订阅的事件类型
}

// Message SSE消息
type Message struct {
	Event     string      `json:"event"`
	Data      interface{} `json:"data"`
	ID        string      `json:"id,omitempty"`
	Retry     int         `json:"retry,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// Manager SSE管理器
type Manager struct {
	connections   map[string]*Connection             // 所有连接 [connectionID]
	userConns     map[uint]map[string]bool           // 用户连接映射 [userID][connectionID]
	typeConns     map[ConnectionType]map[string]bool // 类型连接映射 [type][connectionID]
	mutex         sync.RWMutex
	stopChan      chan struct{}
	pingTicker    *time.Ticker
	cleanupTicker *time.Ticker
	stats         *Stats
	config        *Config
}

// Stats 统计信息
type Stats struct {
	TotalConnections    int64                    `json:"total_connections"`
	ActiveConnections   int64                    `json:"active_connections"`
	ConnectionsByType   map[ConnectionType]int64 `json:"connections_by_type"`
	ConnectionsByUser   map[uint]int64           `json:"connections_by_user"`
	MessagesSent        int64                    `json:"messages_sent"`
	MessagesDropped     int64                    `json:"messages_dropped"`
	LastCleanup         time.Time                `json:"last_cleanup"`
	AverageResponseTime float64                  `json:"average_response_time"`
	mutex               sync.RWMutex
}

// Config 配置
type Config struct {
	MaxConnections        int           `json:"max_connections"`          // 最大连接数
	MaxConnectionsPerUser int           `json:"max_connections_per_user"` // 每用户最大连接数
	PingInterval          time.Duration `json:"ping_interval"`            // 心跳间隔
	ConnectionTimeout     time.Duration `json:"connection_timeout"`       // 连接超时
	CleanupInterval       time.Duration `json:"cleanup_interval"`         // 清理间隔
	MessageBufferSize     int           `json:"message_buffer_size"`      // 消息缓冲区大小
	WriteTimeout          time.Duration `json:"write_timeout"`            // 写入超时
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		MaxConnections:        1000,
		MaxConnectionsPerUser: 10,
		PingInterval:          30 * time.Second,
		ConnectionTimeout:     5 * time.Minute,
		CleanupInterval:       1 * time.Minute,
		MessageBufferSize:     100,
		WriteTimeout:          10 * time.Second,
	}
}

// NewManager 创建新的SSE管理器
func NewManager(config *Config) *Manager {
	if config == nil {
		config = DefaultConfig()
	}

	return &Manager{
		connections:   make(map[string]*Connection),
		userConns:     make(map[uint]map[string]bool),
		typeConns:     make(map[ConnectionType]map[string]bool),
		stopChan:      make(chan struct{}),
		pingTicker:    time.NewTicker(config.PingInterval),
		cleanupTicker: time.NewTicker(config.CleanupInterval),
		stats: &Stats{
			ConnectionsByType: make(map[ConnectionType]int64),
			ConnectionsByUser: make(map[uint]int64),
		},
		config: config,
	}
}

// Start 启动管理器
func (m *Manager) Start() {
	global.LOG.Info("SSE管理器启动")

	go m.pingLoop()
	go m.cleanupLoop()

	global.LOG.Info("SSE管理器启动完成")
}

// Stop 停止管理器
func (m *Manager) Stop() {
	global.LOG.Info("SSE管理器停止中...")

	close(m.stopChan)
	m.pingTicker.Stop()
	m.cleanupTicker.Stop()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭所有连接
	for _, conn := range m.connections {
		m.closeConnection(conn)
	}

	// 清空映射
	m.connections = make(map[string]*Connection)
	m.userConns = make(map[uint]map[string]bool)
	m.typeConns = make(map[ConnectionType]map[string]bool)

	global.LOG.Info("SSE管理器已停止")
}

// AddConnection 添加连接
func (m *Manager) AddConnection(userID uint, connType ConnectionType, w gin.ResponseWriter, r *http.Request) (*Connection, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查连接数限制
	if len(m.connections) >= m.config.MaxConnections {
		return nil, fmt.Errorf("达到最大连接数限制: %d", m.config.MaxConnections)
	}

	// 检查用户连接数限制
	if userConns := m.userConns[userID]; len(userConns) >= m.config.MaxConnectionsPerUser {
		return nil, fmt.Errorf("用户连接数达到限制: %d", m.config.MaxConnectionsPerUser)
	}

	// 创建连接
	ctx, cancel := context.WithCancel(r.Context())
	connID := fmt.Sprintf("%s_%d_%d", connType, userID, time.Now().UnixNano())

	conn := &Connection{
		ID:            connID,
		UserID:        userID,
		Type:          connType,
		Writer:        w,
		Request:       r,
		Context:       ctx,
		Cancel:        cancel,
		Channel:       make(chan Message, m.config.MessageBufferSize),
		LastPing:      time.Now(),
		CreatedAt:     time.Now(),
		RemoteAddr:    r.RemoteAddr,
		Subscriptions: make(map[string]bool),
	}

	// 添加到映射
	m.connections[connID] = conn

	if m.userConns[userID] == nil {
		m.userConns[userID] = make(map[string]bool)
	}
	m.userConns[userID][connID] = true

	if m.typeConns[connType] == nil {
		m.typeConns[connType] = make(map[string]bool)
	}
	m.typeConns[connType][connID] = true

	// 更新统计
	m.updateStats(func(s *Stats) {
		s.TotalConnections++
		s.ActiveConnections++
		s.ConnectionsByType[connType]++
		s.ConnectionsByUser[userID]++
	})

	global.LOG.Info("SSE连接已添加",
		zap.String("connID", connID),
		zap.Uint("userID", userID),
		zap.String("type", string(connType)),
		zap.String("remoteAddr", r.RemoteAddr))

	return conn, nil
}

// RemoveConnection 移除连接
func (m *Manager) RemoveConnection(connID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	conn, exists := m.connections[connID]
	if !exists {
		return
	}

	m.removeConnectionUnsafe(conn)
}

// removeConnectionUnsafe 移除连接（不加锁）
func (m *Manager) removeConnectionUnsafe(conn *Connection) {
	// 从映射中移除
	delete(m.connections, conn.ID)

	if userConns := m.userConns[conn.UserID]; userConns != nil {
		delete(userConns, conn.ID)
		if len(userConns) == 0 {
			delete(m.userConns, conn.UserID)
		}
	}

	if typeConns := m.typeConns[conn.Type]; typeConns != nil {
		delete(typeConns, conn.ID)
		if len(typeConns) == 0 {
			delete(m.typeConns, conn.Type)
		}
	}

	// 关闭连接
	m.closeConnection(conn)

	// 更新统计
	m.updateStats(func(s *Stats) {
		s.ActiveConnections--
		s.ConnectionsByType[conn.Type]--
		s.ConnectionsByUser[conn.UserID]--
		if s.ConnectionsByUser[conn.UserID] <= 0 {
			delete(s.ConnectionsByUser, conn.UserID)
		}
	})

	global.LOG.Info("SSE连接已移除",
		zap.String("connID", conn.ID),
		zap.Uint("userID", conn.UserID),
		zap.String("type", string(conn.Type)))
}

// closeConnection 关闭连接
func (m *Manager) closeConnection(conn *Connection) {
	conn.Cancel()
	close(conn.Channel)
}

// SendToConnection 发送消息到指定连接
func (m *Manager) SendToConnection(connID string, event string, data interface{}) error {
	m.mutex.RLock()
	conn, exists := m.connections[connID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("连接不存在: %s", connID)
	}

	message := Message{
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
	}

	select {
	case conn.Channel <- message:
		m.updateStats(func(s *Stats) {
			s.MessagesSent++
		})
		return nil
	case <-time.After(m.config.WriteTimeout):
		m.updateStats(func(s *Stats) {
			s.MessagesDropped++
		})
		return fmt.Errorf("发送消息超时")
	}
}

// SendToUser 发送消息到用户的所有连接
func (m *Manager) SendToUser(userID uint, event string, data interface{}) error {
	m.mutex.RLock()
	userConns := m.userConns[userID]
	m.mutex.RUnlock()

	if len(userConns) == 0 {
		return fmt.Errorf("用户没有活跃连接: %d", userID)
	}

	var errors []string
	for connID := range userConns {
		if err := m.SendToConnection(connID, event, data); err != nil {
			errors = append(errors, fmt.Sprintf("连接%s: %v", connID, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %v", errors)
	}

	return nil
}

// SendToUserByType 发送消息到用户指定类型的连接
func (m *Manager) SendToUserByType(userID uint, connType ConnectionType, event string, data interface{}) error {
	m.mutex.RLock()
	userConns := m.userConns[userID]
	typeConns := m.typeConns[connType]
	m.mutex.RUnlock()

	if len(userConns) == 0 {
		return fmt.Errorf("用户没有活跃连接: %d", userID)
	}

	var targetConns []string
	for connID := range userConns {
		if typeConns[connID] {
			targetConns = append(targetConns, connID)
		}
	}

	if len(targetConns) == 0 {
		return fmt.Errorf("用户没有指定类型的连接: %d, %s", userID, connType)
	}

	var errors []string
	for _, connID := range targetConns {
		if err := m.SendToConnection(connID, event, data); err != nil {
			errors = append(errors, fmt.Sprintf("连接%s: %v", connID, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %v", errors)
	}

	return nil
}

// BroadcastToType 广播消息到指定类型的所有连接
func (m *Manager) BroadcastToType(connType ConnectionType, event string, data interface{}) error {
	m.mutex.RLock()
	typeConns := m.typeConns[connType]
	m.mutex.RUnlock()

	if len(typeConns) == 0 {
		return fmt.Errorf("没有指定类型的连接: %s", connType)
	}

	var errors []string
	for connID := range typeConns {
		if err := m.SendToConnection(connID, event, data); err != nil {
			errors = append(errors, fmt.Sprintf("连接%s: %v", connID, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %v", errors)
	}

	return nil
}

// BroadcastToAll 广播消息到所有连接
func (m *Manager) BroadcastToAll(event string, data interface{}) error {
	m.mutex.RLock()
	connIDs := make([]string, 0, len(m.connections))
	for connID := range m.connections {
		connIDs = append(connIDs, connID)
	}
	m.mutex.RUnlock()

	if len(connIDs) == 0 {
		return fmt.Errorf("没有活跃连接")
	}

	var errors []string
	for _, connID := range connIDs {
		if err := m.SendToConnection(connID, event, data); err != nil {
			errors = append(errors, fmt.Sprintf("连接%s: %v", connID, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %v", errors)
	}

	return nil
}

// Subscribe 订阅事件
func (m *Manager) Subscribe(connID string, eventType string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	conn, exists := m.connections[connID]
	if !exists {
		return fmt.Errorf("连接不存在: %s", connID)
	}

	conn.Subscriptions[eventType] = true
	return nil
}

// Unsubscribe 取消订阅事件
func (m *Manager) Unsubscribe(connID string, eventType string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	conn, exists := m.connections[connID]
	if !exists {
		return fmt.Errorf("连接不存在: %s", connID)
	}

	delete(conn.Subscriptions, eventType)
	return nil
}

// SendToSubscribers 发送消息到订阅者
func (m *Manager) SendToSubscribers(eventType string, event string, data interface{}) error {
	m.mutex.RLock()
	var targetConns []string
	for connID, conn := range m.connections {
		if conn.Subscriptions[eventType] {
			targetConns = append(targetConns, connID)
		}
	}
	m.mutex.RUnlock()

	if len(targetConns) == 0 {
		return fmt.Errorf("没有订阅者: %s", eventType)
	}

	var errors []string
	for _, connID := range targetConns {
		if err := m.SendToConnection(connID, event, data); err != nil {
			errors = append(errors, fmt.Sprintf("连接%s: %v", connID, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分消息发送失败: %v", errors)
	}

	return nil
}

// HandleConnection 处理SSE连接
func (m *Manager) HandleConnection(conn *Connection) {
	// 设置SSE响应头
	conn.Writer.Header().Set("Content-Type", "text/event-stream")
	conn.Writer.Header().Set("Cache-Control", "no-cache")
	conn.Writer.Header().Set("Connection", "keep-alive")
	conn.Writer.Header().Set("Access-Control-Allow-Origin", "*")
	conn.Writer.Header().Set("Access-Control-Allow-Headers", "Cache-Control")
	conn.Writer.Header().Set("X-Accel-Buffering", "no")

	// 发送连接确认
	m.sendMessage(conn, "connected", map[string]interface{}{
		"type":      "connected",
		"message":   "SSE连接已建立",
		"timestamp": time.Now().Unix(),
	})

	// 处理消息循环
	for {
		select {
		case <-conn.Context.Done():
			global.LOG.Info("SSE连接上下文已取消", zap.String("connID", conn.ID))
			m.RemoveConnection(conn.ID)
			return
		case message, ok := <-conn.Channel:
			if !ok {
				global.LOG.Info("SSE连接通道已关闭", zap.String("connID", conn.ID))
				return
			}
			if err := m.sendMessage(conn, message.Event, message.Data); err != nil {
				global.LOG.Error("发送SSE消息失败",
					zap.String("connID", conn.ID),
					zap.Error(err))
				m.RemoveConnection(conn.ID)
				return
			}
		}
	}
}

// sendMessage 发送SSE消息
func (m *Manager) sendMessage(conn *Connection, event string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 构建SSE消息格式
	message := fmt.Sprintf("event: %s\ndata: %s\n\n", event, string(jsonData))

	// 写入响应
	if _, err := conn.Writer.Write([]byte(message)); err != nil {
		return fmt.Errorf("写入响应失败: %v", err)
	}

	// 刷新缓冲区
	if flusher, ok := conn.Writer.(http.Flusher); ok {
		flusher.Flush()
	}

	// 更新最后心跳时间
	conn.LastPing = time.Now()

	return nil
}

// pingLoop 心跳循环
func (m *Manager) pingLoop() {
	for {
		select {
		case <-m.stopChan:
			return
		case <-m.pingTicker.C:
			m.sendPingToAll()
		}
	}
}

// sendPingToAll 发送心跳到所有连接
func (m *Manager) sendPingToAll() {
	m.mutex.RLock()
	connIDs := make([]string, 0, len(m.connections))
	for connID := range m.connections {
		connIDs = append(connIDs, connID)
	}
	m.mutex.RUnlock()

	pingData := map[string]interface{}{
		"type":      "ping",
		"timestamp": time.Now().Unix(),
	}

	for _, connID := range connIDs {
		if err := m.SendToConnection(connID, "ping", pingData); err != nil {
			global.LOG.Debug("发送心跳失败",
				zap.String("connID", connID),
				zap.Error(err))
		}
	}
}

// cleanupLoop 清理循环
func (m *Manager) cleanupLoop() {
	for {
		select {
		case <-m.stopChan:
			return
		case <-m.cleanupTicker.C:
			m.cleanupExpiredConnections()
		}
	}
}

// cleanupExpiredConnections 清理过期连接
func (m *Manager) cleanupExpiredConnections() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	var expiredConns []*Connection

	for _, conn := range m.connections {
		if now.Sub(conn.LastPing) > m.config.ConnectionTimeout {
			expiredConns = append(expiredConns, conn)
		}
	}

	for _, conn := range expiredConns {
		global.LOG.Info("清理过期连接",
			zap.String("connID", conn.ID),
			zap.Duration("timeout", now.Sub(conn.LastPing)))
		m.removeConnectionUnsafe(conn)
	}

	// 更新清理时间
	m.updateStats(func(s *Stats) {
		s.LastCleanup = now
	})

	if len(expiredConns) > 0 {
		global.LOG.Info("清理完成", zap.Int("cleaned", len(expiredConns)))
	}
}

// updateStats 更新统计信息
func (m *Manager) updateStats(fn func(*Stats)) {
	m.stats.mutex.Lock()
	defer m.stats.mutex.Unlock()
	fn(m.stats)
}

// GetStats 获取统计信息
func (m *Manager) GetStats() *Stats {
	m.stats.mutex.RLock()
	defer m.stats.mutex.RUnlock()

	// 创建副本
	stats := &Stats{
		TotalConnections:    m.stats.TotalConnections,
		ActiveConnections:   m.stats.ActiveConnections,
		ConnectionsByType:   make(map[ConnectionType]int64),
		ConnectionsByUser:   make(map[uint]int64),
		MessagesSent:        m.stats.MessagesSent,
		MessagesDropped:     m.stats.MessagesDropped,
		LastCleanup:         m.stats.LastCleanup,
		AverageResponseTime: m.stats.AverageResponseTime,
	}

	for k, v := range m.stats.ConnectionsByType {
		stats.ConnectionsByType[k] = v
	}
	for k, v := range m.stats.ConnectionsByUser {
		stats.ConnectionsByUser[k] = v
	}

	return stats
}

// GetConnections 获取连接信息
func (m *Manager) GetConnections() map[string]*Connection {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	connections := make(map[string]*Connection)
	for k, v := range m.connections {
		connections[k] = v
	}
	return connections
}

// GetConnectionsByUser 获取用户的连接
func (m *Manager) GetConnectionsByUser(userID uint) []*Connection {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var connections []*Connection
	if userConns := m.userConns[userID]; userConns != nil {
		for connID := range userConns {
			if conn := m.connections[connID]; conn != nil {
				connections = append(connections, conn)
			}
		}
	}
	return connections
}

// GetConnectionsByType 获取指定类型的连接
func (m *Manager) GetConnectionsByType(connType ConnectionType) []*Connection {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var connections []*Connection
	if typeConns := m.typeConns[connType]; typeConns != nil {
		for connID := range typeConns {
			if conn := m.connections[connID]; conn != nil {
				connections = append(connections, conn)
			}
		}
	}
	return connections
}
