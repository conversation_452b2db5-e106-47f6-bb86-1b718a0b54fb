package ssemgr

import (
	"time"

	"server/global"

	"go.uber.org/zap"
)

// DataProvider 数据提供者接口
type DataProvider interface {
	GetDashboardStats() (interface{}, error)
	GetSystemInfo() (interface{}, error)
	GetNetworkTopology() (interface{}, error)
}

// PusherAdapter 推送适配器
type PusherAdapter struct {
	pusher       *Pusher
	dataProvider DataProvider
}

// NewPusherAdapter 创建推送适配器
func NewPusherAdapter(dataProvider DataProvider) *PusherAdapter {
	return &PusherAdapter{
		pusher:       NewPusher(),
		dataProvider: dataProvider,
	}
}

// Start 启动适配器
func (pa *PusherAdapter) Start() {
	pa.pusher.Start()
	
	// 启动数据推送器
	go pa.startDashboardDataPusher()
	
	global.LOG.Info("推送适配器已启动")
}

// Stop 停止适配器
func (pa *PusherAdapter) Stop() {
	pa.pusher.Stop()
	global.LOG.Info("推送适配器已停止")
}

// startDashboardDataPusher 启动仪表盘数据推送器
func (pa *PusherAdapter) startDashboardDataPusher() {
	if pa.dataProvider == nil {
		global.LOG.Warn("数据提供者为空，跳过仪表盘数据推送")
		return
	}

	// 系统信息推送器
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-pa.pusher.ctx.Done():
				return
			case <-ticker.C:
				pa.pushSystemInfo()
			}
		}
	}()

	// 统计数据推送器
	go func() {
		ticker := time.NewTicker(3 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-pa.pusher.ctx.Done():
				return
			case <-ticker.C:
				pa.pushDashboardStats()
			}
		}
	}()

	// 网络拓扑推送器
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-pa.pusher.ctx.Done():
				return
			case <-ticker.C:
				pa.pushNetworkTopology()
			}
		}
	}()
}

// pushSystemInfo 推送系统信息
func (pa *PusherAdapter) pushSystemInfo() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送系统信息时发生panic", zap.Any("panic", r))
		}
	}()

	systemInfo, err := pa.dataProvider.GetSystemInfo()
	if err != nil {
		global.LOG.Debug("获取系统信息失败", zap.Error(err))
		return
	}

	data := map[string]interface{}{
		"type":      "system_info",
		"data":      systemInfo,
		"timestamp": time.Now().Unix(),
	}

	if err := BroadcastToType(TypeDashboard, "system_info", data); err != nil {
		global.LOG.Debug("推送系统信息失败", zap.Error(err))
	}
}

// pushDashboardStats 推送仪表盘统计数据
func (pa *PusherAdapter) pushDashboardStats() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送仪表盘数据时发生panic", zap.Any("panic", r))
		}
	}()

	stats, err := pa.dataProvider.GetDashboardStats()
	if err != nil {
		global.LOG.Debug("获取仪表盘统计数据失败", zap.Error(err))
		return
	}

	data := map[string]interface{}{
		"type":      "dashboard_stats",
		"data":      stats,
		"timestamp": time.Now().Unix(),
	}

	if err := BroadcastToType(TypeDashboard, "dashboard_stats", data); err != nil {
		global.LOG.Debug("推送仪表盘统计数据失败", zap.Error(err))
	}
}

// pushNetworkTopology 推送网络拓扑数据
func (pa *PusherAdapter) pushNetworkTopology() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送网络拓扑数据时发生panic", zap.Any("panic", r))
		}
	}()

	topology, err := pa.dataProvider.GetNetworkTopology()
	if err != nil {
		global.LOG.Debug("获取网络拓扑数据失败", zap.Error(err))
		return
	}

	data := map[string]interface{}{
		"type":      "topology_data",
		"data":      topology,
		"timestamp": time.Now().Unix(),
	}

	if err := BroadcastToType(TypeDashboard, "topology_data", data); err != nil {
		global.LOG.Debug("推送网络拓扑数据失败", zap.Error(err))
	}
}

// PushNotification 推送通知消息
func (pa *PusherAdapter) PushNotification(userID uint, notification interface{}) {
	pa.pusher.PushNotification(userID, notification)
}

// PushLogUpdate 推送日志更新
func (pa *PusherAdapter) PushLogUpdate(userID uint, logData interface{}) {
	pa.pusher.PushLogUpdate(userID, logData)
}

// PushNetworkProgress 推送网络进度
func (pa *PusherAdapter) PushNetworkProgress(userID uint, progress interface{}) {
	pa.pusher.PushNetworkProgress(userID, progress)
}

// 全局推送适配器实例
var GlobalPusherAdapter *PusherAdapter

// InitPusherAdapter 初始化全局推送适配器
func InitPusherAdapter(dataProvider DataProvider) {
	GlobalPusherAdapter = NewPusherAdapter(dataProvider)
	GlobalPusherAdapter.Start()
	global.LOG.Info("全局推送适配器初始化完成")
}

// StopPusherAdapter 停止全局推送适配器
func StopPusherAdapter() {
	if GlobalPusherAdapter != nil {
		GlobalPusherAdapter.Stop()
		GlobalPusherAdapter = nil
		global.LOG.Info("全局推送适配器已停止")
	}
}

// GetPusherAdapter 获取全局推送适配器
func GetPusherAdapter() *PusherAdapter {
	return GlobalPusherAdapter
}
