package ssemgr

import (
	"context"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// Pusher 数据推送服务
type Pusher struct {
	ctx    context.Context
	cancel context.CancelFunc
}

// NewPusher 创建数据推送服务
func NewPusher() *Pusher {
	ctx, cancel := context.WithCancel(context.Background())
	return &Pusher{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动数据推送服务
func (p *Pusher) Start() {
	global.LOG.Info("启动数据推送服务")

	// 启动各种数据推送器
	go p.startDashboardPusher()
	go p.startSystemInfoPusher()
	go p.startNetworkTopologyPusher()

	global.LOG.Info("所有数据推送器已启动")
}

// Stop 停止数据推送服务
func (p *Pusher) Stop() {
	global.LOG.Info("停止数据推送服务")
	p.cancel()
}

// startDashboardPusher 启动仪表盘数据推送器
func (p *Pusher) startDashboardPusher() {
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			p.pushDashboardStats()
		}
	}
}

// startSystemInfoPusher 启动系统信息推送器
func (p *Pusher) startSystemInfoPusher() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			p.pushSystemInfo()
		}
	}
}

// startNetworkTopologyPusher 启动网络拓扑推送器
func (p *Pusher) startNetworkTopologyPusher() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			p.pushNetworkTopology()
		}
	}
}

// pushDashboardStats 推送仪表盘统计数据
func (p *Pusher) pushDashboardStats() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送仪表盘数据时发生panic", zap.Any("panic", r))
		}
	}()

	// 这里应该通过回调函数或接口来获取数据，避免循环依赖
	// 暂时发送一个简单的心跳消息
	data := map[string]interface{}{
		"type":      "dashboard_stats",
		"timestamp": time.Now().Unix(),
	}

	if err := BroadcastToType(TypeDashboard, "dashboard_stats", data); err != nil {
		global.LOG.Debug("推送仪表盘统计数据失败", zap.Error(err))
	}
}

// pushSystemInfo 推送系统信息
func (p *Pusher) pushSystemInfo() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送系统信息时发生panic", zap.Any("panic", r))
		}
	}()

	// 这里应该通过回调函数或接口来获取数据，避免循环依赖
	data := map[string]interface{}{
		"type":      "system_info",
		"timestamp": time.Now().Unix(),
	}

	if err := BroadcastToType(TypeDashboard, "system_info", data); err != nil {
		global.LOG.Debug("推送系统信息失败", zap.Error(err))
	}
}

// pushNetworkTopology 推送网络拓扑数据
func (p *Pusher) pushNetworkTopology() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送网络拓扑数据时发生panic", zap.Any("panic", r))
		}
	}()

	// 这里应该通过回调函数或接口来获取数据，避免循环依赖
	data := map[string]interface{}{
		"type":      "topology_data",
		"timestamp": time.Now().Unix(),
	}

	if err := BroadcastToType(TypeDashboard, "topology_data", data); err != nil {
		global.LOG.Debug("推送网络拓扑数据失败", zap.Error(err))
	}
}

// PushNotification 推送通知消息
func (p *Pusher) PushNotification(userID uint, notification interface{}) {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送通知消息时发生panic", zap.Any("panic", r))
		}
	}()

	data := map[string]interface{}{
		"type":         "notification",
		"action":       "new",
		"notification": notification,
		"timestamp":    time.Now().Unix(),
	}

	var err error
	if userID == 0 {
		// 广播通知
		err = BroadcastToType(TypeNotification, "notification", data)
	} else {
		// 发送给特定用户
		err = SendToUserByType(userID, TypeNotification, "notification", data)
	}

	if err != nil {
		global.LOG.Error("推送通知失败",
			zap.Uint("userID", userID),
			zap.Error(err))
	}
}

// PushLogUpdate 推送日志更新
func (p *Pusher) PushLogUpdate(userID uint, logData interface{}) {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送日志更新时发生panic", zap.Any("panic", r))
		}
	}()

	data := map[string]interface{}{
		"type":      "log_update",
		"data":      logData,
		"timestamp": time.Now().Unix(),
	}

	var err error
	if userID == 0 {
		err = BroadcastToType(TypeLog, "log_update", data)
	} else {
		err = SendToUserByType(userID, TypeLog, "log_update", data)
	}

	if err != nil {
		global.LOG.Error("推送日志更新失败",
			zap.Uint("userID", userID),
			zap.Error(err))
	}
}

// PushNetworkProgress 推送网络进度
func (p *Pusher) PushNetworkProgress(userID uint, progress interface{}) {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送网络进度时发生panic", zap.Any("panic", r))
		}
	}()

	data := map[string]interface{}{
		"type":      "interface_progress",
		"data":      progress,
		"timestamp": time.Now().Unix(),
	}

	var err error
	if userID == 0 {
		err = BroadcastToType(TypeNetwork, "interface_progress", data)
	} else {
		err = SendToUserByType(userID, TypeNetwork, "interface_progress", data)
	}

	if err != nil {
		global.LOG.Error("推送网络进度失败",
			zap.Uint("userID", userID),
			zap.Error(err))
	}
}

// 全局推送器实例
var GlobalPusher *Pusher

// InitPusher 初始化全局推送器
func InitPusher() {
	GlobalPusher = NewPusher()
	GlobalPusher.Start()
	global.LOG.Info("全局数据推送器初始化完成")
}

// StopPusher 停止全局推送器
func StopPusher() {
	if GlobalPusher != nil {
		GlobalPusher.Stop()
		GlobalPusher = nil
		global.LOG.Info("全局数据推送器已停止")
	}
}

// GetPusher 获取全局推送器
func GetPusher() *Pusher {
	return GlobalPusher
}
