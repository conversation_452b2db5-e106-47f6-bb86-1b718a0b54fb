package unifiedsse

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"server/global"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// SSEConnection SSE连接实现
type SSEConnection struct {
	// 基本信息
	id         string
	userID     uint
	remoteAddr string
	userAgent  string
	createdAt  time.Time

	// 连接状态
	ctx     context.Context
	cancel  context.CancelFunc
	writer  gin.ResponseWriter
	request *http.Request
	flusher http.Flusher
	closed  bool
	mu      sync.RWMutex

	// 配置
	options *ConnectionOptions

	// 订阅管理
	subscriptions map[string]bool
	subMu         sync.RWMutex

	// 消息队列
	messageQueue chan *Message
	queueMu      sync.Mutex

	// 统计信息
	stats        *ConnectionStats
	lastActivity time.Time

	// 心跳管理
	heartbeatTicker *time.Ticker
	heartbeatDone   chan struct{}

	// 管理器引用
	manager *UnifiedSSEManager
}

// NewSSEConnection 创建新的SSE连接
func NewSSEConnection(ctx context.Context, userID uint, w gin.ResponseWriter, r *http.Request, options *ConnectionOptions, manager *UnifiedSSEManager) (*SSEConnection, error) {
	// 检查是否支持SSE
	flusher, ok := w.(http.Flusher)
	if !ok {
		return nil, &SSEError{
			Code:    ErrCodeInternalError,
			Message: "响应不支持流式传输",
		}
	}

	// 设置SSE响应头
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Cache-Control")
	w.Header().Set("X-Accel-Buffering", "no") // 禁用nginx缓冲

	connCtx, cancel := context.WithCancel(ctx)

	conn := &SSEConnection{
		id:            uuid.New().String(),
		userID:        userID,
		remoteAddr:    r.RemoteAddr,
		userAgent:     r.UserAgent(),
		createdAt:     time.Now(),
		ctx:           connCtx,
		cancel:        cancel,
		writer:        w,
		request:       r,
		flusher:       flusher,
		options:       options,
		subscriptions: make(map[string]bool),
		messageQueue:  make(chan *Message, options.BufferSize),
		stats: &ConnectionStats{
			LastActivity: time.Now(),
		},
		lastActivity:  time.Now(),
		heartbeatDone: make(chan struct{}),
		manager:       manager,
	}

	// 启动消息处理协程
	go conn.messageLoop()

	// 启动心跳
	if options.HeartbeatInterval > 0 {
		go conn.startHeartbeat()
	}

	// 发送连接确认消息
	conn.SendEvent("connected", map[string]interface{}{
		"connectionId": conn.id,
		"timestamp":    time.Now().Unix(),
		"message":      "SSE连接已建立",
	})

	return conn, nil
}

// ID 获取连接ID
func (c *SSEConnection) ID() string {
	return c.id
}

// UserID 获取用户ID
func (c *SSEConnection) UserID() uint {
	return c.userID
}

// RemoteAddr 获取远程地址
func (c *SSEConnection) RemoteAddr() string {
	return c.remoteAddr
}

// UserAgent 获取用户代理
func (c *SSEConnection) UserAgent() string {
	return c.userAgent
}

// CreatedAt 获取创建时间
func (c *SSEConnection) CreatedAt() time.Time {
	return c.createdAt
}

// LastActivity 获取最后活动时间
func (c *SSEConnection) LastActivity() time.Time {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastActivity
}

// IsActive 检查连接是否活跃
func (c *SSEConnection) IsActive() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.closed {
		return false
	}

	// 检查上下文是否已取消
	select {
	case <-c.ctx.Done():
		return false
	default:
		return true
	}
}

// IsClosed 检查连接是否已关闭
func (c *SSEConnection) IsClosed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.closed
}

// Context 获取连接上下文
func (c *SSEConnection) Context() context.Context {
	return c.ctx
}

// Send 发送消息
func (c *SSEConnection) Send(message *Message) error {
	if c.IsClosed() {
		return &SSEError{
			Code:    ErrCodeConnectionClosed,
			Message: "连接已关闭",
		}
	}

	// 检查消息大小
	messageData, err := json.Marshal(message)
	if err != nil {
		return &SSEError{
			Code:    ErrCodeInvalidMessage,
			Message: "消息序列化失败",
			Details: err.Error(),
		}
	}

	if len(messageData) > c.options.MaxMessageSize {
		return &SSEError{
			Code:    ErrCodeMessageTooLarge,
			Message: "消息过大",
		}
	}

	// 尝试发送到队列
	select {
	case c.messageQueue <- message:
		return nil
	case <-time.After(5 * time.Second):
		return &SSEError{
			Code:    ErrCodeInternalError,
			Message: "消息队列已满",
		}
	case <-c.ctx.Done():
		return &SSEError{
			Code:    ErrCodeConnectionClosed,
			Message: "连接已关闭",
		}
	}
}

// SendEvent 发送事件
func (c *SSEConnection) SendEvent(event string, data interface{}) error {
	message := &Message{
		ID:        uuid.New().String(),
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
		Priority:  PriorityNormal,
	}
	return c.Send(message)
}

// SendRaw 发送原始数据
func (c *SSEConnection) SendRaw(data []byte) error {
	if c.IsClosed() {
		return &SSEError{
			Code:    ErrCodeConnectionClosed,
			Message: "连接已关闭",
		}
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 写入数据
	if _, err := c.writer.Write(data); err != nil {
		return err
	}

	// 刷新缓冲区
	c.flusher.Flush()

	// 更新统计
	c.updateStats(len(data))

	return nil
}

// Subscribe 订阅主题
func (c *SSEConnection) Subscribe(topics ...string) error {
	c.subMu.Lock()
	defer c.subMu.Unlock()

	for _, topic := range topics {
		c.subscriptions[topic] = true
	}

	global.LOG.Debug("连接订阅主题",
		zap.String("connectionID", c.id),
		zap.Strings("topics", topics))

	return nil
}

// Unsubscribe 取消订阅主题
func (c *SSEConnection) Unsubscribe(topics ...string) error {
	c.subMu.Lock()
	defer c.subMu.Unlock()

	for _, topic := range topics {
		delete(c.subscriptions, topic)
	}

	global.LOG.Debug("连接取消订阅主题",
		zap.String("connectionID", c.id),
		zap.Strings("topics", topics))

	return nil
}

// GetSubscriptions 获取订阅列表
func (c *SSEConnection) GetSubscriptions() []string {
	c.subMu.RLock()
	defer c.subMu.RUnlock()

	topics := make([]string, 0, len(c.subscriptions))
	for topic := range c.subscriptions {
		topics = append(topics, topic)
	}
	return topics
}

// Close 关闭连接
func (c *SSEConnection) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return nil
	}

	c.closed = true

	// 取消上下文
	c.cancel()

	// 停止心跳
	if c.heartbeatTicker != nil {
		c.heartbeatTicker.Stop()
		close(c.heartbeatDone)
	}

	// 关闭消息队列
	close(c.messageQueue)

	global.LOG.Info("SSE连接已关闭",
		zap.String("connectionID", c.id),
		zap.Uint("userID", c.userID))

	return nil
}

// Ping 发送心跳
func (c *SSEConnection) Ping() error {
	return c.SendEvent("ping", map[string]interface{}{
		"timestamp": time.Now().Unix(),
	})
}

// GetStats 获取连接统计
func (c *SSEConnection) GetStats() *ConnectionStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stats := *c.stats
	stats.ConnectedTime = time.Since(c.createdAt)
	stats.SubscriptionCount = len(c.subscriptions)
	return &stats
}

// messageLoop 消息处理循环
func (c *SSEConnection) messageLoop() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("消息处理循环panic",
				zap.Any("panic", r),
				zap.String("connectionID", c.id))
		}
		// 确保连接被清理
		c.manager.RemoveConnection(c.id)
	}()

	for {
		select {
		case <-c.ctx.Done():
			return
		case message, ok := <-c.messageQueue:
			if !ok {
				return
			}
			if err := c.writeMessage(message); err != nil {
				global.LOG.Error("写入消息失败",
					zap.Error(err),
					zap.String("connectionID", c.id))
				return
			}
		}
	}
}

// writeMessage 写入消息到SSE流
func (c *SSEConnection) writeMessage(message *Message) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return &SSEError{
			Code:    ErrCodeConnectionClosed,
			Message: "连接已关闭",
		}
	}

	// 序列化消息数据
	data, err := json.Marshal(message.Data)
	if err != nil {
		c.stats.ErrorCount++
		return err
	}

	// 构建SSE消息格式
	var sseMessage string
	if message.ID != "" {
		sseMessage += fmt.Sprintf("id: %s\n", message.ID)
	}
	if message.Event != "" {
		sseMessage += fmt.Sprintf("event: %s\n", message.Event)
	}
	sseMessage += fmt.Sprintf("data: %s\n\n", string(data))

	// 写入数据
	if _, err := c.writer.Write([]byte(sseMessage)); err != nil {
		c.stats.ErrorCount++
		return err
	}

	// 刷新缓冲区
	c.flusher.Flush()

	// 更新统计
	c.updateStats(len(sseMessage))

	return nil
}

// startHeartbeat 启动心跳
func (c *SSEConnection) startHeartbeat() {
	c.heartbeatTicker = time.NewTicker(c.options.HeartbeatInterval)
	defer c.heartbeatTicker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-c.heartbeatDone:
			return
		case <-c.heartbeatTicker.C:
			if err := c.Ping(); err != nil {
				global.LOG.Error("发送心跳失败",
					zap.Error(err),
					zap.String("connectionID", c.id))
				return
			}
		}
	}
}

// updateStats 更新统计信息
func (c *SSEConnection) updateStats(messageSize int) {
	c.stats.MessagesSent++
	c.stats.BytesSent += int64(messageSize)
	c.lastActivity = time.Now()
	c.stats.LastActivity = c.lastActivity
}

// isSubscribedTo 检查是否订阅了主题
func (c *SSEConnection) isSubscribedTo(topic string) bool {
	c.subMu.RLock()
	defer c.subMu.RUnlock()
	return c.subscriptions[topic]
}
