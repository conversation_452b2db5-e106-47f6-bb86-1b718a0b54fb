package unifiedsse

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// MessageRouterImpl 消息路由器实现
type MessageRouterImpl struct {
	// 路由映射
	routes map[string]MessageHandler
	mu     sync.RWMutex

	// 中间件
	middlewares []MiddlewareFunc

	// 通配符路由
	wildcardRoutes map[string]MessageHandler
}

// NewMessageRouter 创建新的消息路由器
func NewMessageRouter() MessageRouter {
	return &MessageRouterImpl{
		routes:         make(map[string]MessageHandler),
		middlewares:    make([]MiddlewareFunc, 0),
		wildcardRoutes: make(map[string]MessageHandler),
	}
}

// AddRoute 添加路由
func (r *MessageRouterImpl) AddRoute(topic string, handler MessageHandler) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if handler == nil {
		return &SSEError{
			Code:    ErrCodeInvalidMessage,
			Message: "处理器不能为空",
		}
	}

	// 检查是否为通配符路由
	if strings.Contains(topic, "*") {
		r.wildcardRoutes[topic] = handler
	} else {
		r.routes[topic] = handler
	}

	global.LOG.Debug("添加消息路由",
		zap.String("topic", topic),
		zap.Bool("wildcard", strings.Contains(topic, "*")))

	return nil
}

// RemoveRoute 移除路由
func (r *MessageRouterImpl) RemoveRoute(topic string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查是否为通配符路由
	if strings.Contains(topic, "*") {
		delete(r.wildcardRoutes, topic)
	} else {
		delete(r.routes, topic)
	}

	global.LOG.Debug("移除消息路由", zap.String("topic", topic))

	return nil
}

// GetRoutes 获取所有路由
func (r *MessageRouterImpl) GetRoutes() map[string]MessageHandler {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 合并普通路由和通配符路由
	allRoutes := make(map[string]MessageHandler)

	for topic, handler := range r.routes {
		allRoutes[topic] = handler
	}

	for topic, handler := range r.wildcardRoutes {
		allRoutes[topic] = handler
	}

	return allRoutes
}

// Route 路由消息
func (r *MessageRouterImpl) Route(message *Message) error {
	if message == nil {
		return &SSEError{
			Code:    ErrCodeInvalidMessage,
			Message: "消息不能为空",
		}
	}

	return r.RouteToTopic(message.Topic, message)
}

// RouteToTopic 路由消息到指定主题
func (r *MessageRouterImpl) RouteToTopic(topic string, message *Message) error {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 查找精确匹配的路由
	if handler, exists := r.routes[topic]; exists {
		return r.executeHandler(nil, message, handler)
	}

	// 查找通配符匹配的路由
	for pattern, handler := range r.wildcardRoutes {
		if r.matchWildcard(pattern, topic) {
			return r.executeHandler(nil, message, handler)
		}
	}

	// 没有找到匹配的路由
	global.LOG.Debug("没有找到匹配的路由", zap.String("topic", topic))
	return nil
}

// Use 添加中间件
func (r *MessageRouterImpl) Use(middleware ...MiddlewareFunc) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	for _, mw := range middleware {
		if mw == nil {
			return &SSEError{
				Code:    ErrCodeInvalidMessage,
				Message: "中间件不能为空",
			}
		}
		r.middlewares = append(r.middlewares, mw)
	}

	global.LOG.Debug("添加中间件", zap.Int("count", len(middleware)))

	return nil
}

// executeHandler 执行处理器（包含中间件链）
func (r *MessageRouterImpl) executeHandler(conn Connection, message *Message, handler MessageHandler) error {
	// 构建中间件链
	finalHandler := func() error {
		return handler(conn, message)
	}

	// 从后往前构建中间件链
	for i := len(r.middlewares) - 1; i >= 0; i-- {
		middleware := r.middlewares[i]
		nextHandler := finalHandler
		finalHandler = func() error {
			return middleware(conn, message, nextHandler)
		}
	}

	// 执行中间件链
	return finalHandler()
}

// matchWildcard 匹配通配符模式
func (r *MessageRouterImpl) matchWildcard(pattern, topic string) bool {
	// 简单的通配符匹配实现
	// 支持 * 匹配任意字符串
	// 支持 ? 匹配单个字符

	return r.wildcardMatch(pattern, topic, 0, 0)
}

// wildcardMatch 递归通配符匹配
func (r *MessageRouterImpl) wildcardMatch(pattern, str string, pIndex, sIndex int) bool {
	pLen := len(pattern)
	sLen := len(str)

	// 如果模式已经匹配完
	if pIndex == pLen {
		return sIndex == sLen
	}

	// 如果遇到 *
	if pattern[pIndex] == '*' {
		// * 可以匹配0个或多个字符
		for i := sIndex; i <= sLen; i++ {
			if r.wildcardMatch(pattern, str, pIndex+1, i) {
				return true
			}
		}
		return false
	}

	// 如果字符串已经匹配完但模式还有字符
	if sIndex == sLen {
		return false
	}

	// 如果遇到 ? 或者字符完全匹配
	if pattern[pIndex] == '?' || pattern[pIndex] == str[sIndex] {
		return r.wildcardMatch(pattern, str, pIndex+1, sIndex+1)
	}

	return false
}

// TopicMatcher 主题匹配器
type TopicMatcher struct {
	patterns map[string]bool
	mu       sync.RWMutex
}

// NewTopicMatcher 创建主题匹配器
func NewTopicMatcher() *TopicMatcher {
	return &TopicMatcher{
		patterns: make(map[string]bool),
	}
}

// AddPattern 添加匹配模式
func (tm *TopicMatcher) AddPattern(pattern string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	tm.patterns[pattern] = true
}

// RemovePattern 移除匹配模式
func (tm *TopicMatcher) RemovePattern(pattern string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	delete(tm.patterns, pattern)
}

// Match 检查主题是否匹配任何模式
func (tm *TopicMatcher) Match(topic string) bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	// 检查精确匹配
	if tm.patterns[topic] {
		return true
	}

	// 检查通配符匹配
	for pattern := range tm.patterns {
		if strings.Contains(pattern, "*") || strings.Contains(pattern, "?") {
			router := &MessageRouterImpl{}
			if router.matchWildcard(pattern, topic) {
				return true
			}
		}
	}

	return false
}

// GetPatterns 获取所有模式
func (tm *TopicMatcher) GetPatterns() []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	patterns := make([]string, 0, len(tm.patterns))
	for pattern := range tm.patterns {
		patterns = append(patterns, pattern)
	}
	return patterns
}

// 预定义的中间件

// LoggingMiddleware 日志中间件
func LoggingMiddleware(conn Connection, message *Message, next func() error) error {
	start := time.Now()

	global.LOG.Debug("处理消息开始",
		zap.String("topic", message.Topic),
		zap.String("event", message.Event),
		zap.String("connectionID", func() string {
			if conn != nil {
				return conn.ID()
			}
			return "unknown"
		}()))

	err := next()

	duration := time.Since(start)
	if err != nil {
		global.LOG.Error("处理消息失败",
			zap.Error(err),
			zap.String("topic", message.Topic),
			zap.Duration("duration", duration))
	} else {
		global.LOG.Debug("处理消息成功",
			zap.String("topic", message.Topic),
			zap.Duration("duration", duration))
	}

	return err
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(maxRequests int, window time.Duration) MiddlewareFunc {
	type rateLimiter struct {
		requests []time.Time
		mu       sync.Mutex
	}

	limiters := make(map[string]*rateLimiter)
	var limitersMu sync.RWMutex

	return func(conn Connection, message *Message, next func() error) error {
		if conn == nil {
			return next()
		}

		connectionID := conn.ID()

		limitersMu.RLock()
		limiter, exists := limiters[connectionID]
		limitersMu.RUnlock()

		if !exists {
			limitersMu.Lock()
			limiter = &rateLimiter{
				requests: make([]time.Time, 0),
			}
			limiters[connectionID] = limiter
			limitersMu.Unlock()
		}

		limiter.mu.Lock()
		defer limiter.mu.Unlock()

		now := time.Now()

		// 清理过期的请求记录
		cutoff := now.Add(-window)
		validRequests := make([]time.Time, 0)
		for _, reqTime := range limiter.requests {
			if reqTime.After(cutoff) {
				validRequests = append(validRequests, reqTime)
			}
		}
		limiter.requests = validRequests

		// 检查是否超过限制
		if len(limiter.requests) >= maxRequests {
			return &SSEError{
				Code:    ErrCodeRateLimitExceeded,
				Message: fmt.Sprintf("请求频率超过限制: %d/%v", maxRequests, window),
			}
		}

		// 记录当前请求
		limiter.requests = append(limiter.requests, now)

		return next()
	}
}
