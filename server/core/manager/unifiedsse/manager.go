package unifiedsse

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"server/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UnifiedSSEManager 统一SSE管理器实现
type UnifiedSSEManager struct {
	// 核心组件
	connectionPool ConnectionPool
	messageRouter  MessageRouter
	monitor        Monitor

	// 配置
	config *ManagerConfig

	// 状态管理
	running   bool
	startTime time.Time
	ctx       context.Context
	cancel    context.CancelFunc
	mu        sync.RWMutex

	// 事件处理
	eventHandlers map[ConnectionEvent][]ConnectionEventHandler

	// 清理任务
	cleanupTicker *time.Ticker
	healthTicker  *time.Ticker
}

// ManagerConfig 管理器配置
type ManagerConfig struct {
	MaxConnections        int           `json:"max_connections"`
	MaxConnectionsPerUser int           `json:"max_connections_per_user"`
	HeartbeatInterval     time.Duration `json:"heartbeat_interval"`
	ConnectionTimeout     time.Duration `json:"connection_timeout"`
	CleanupInterval       time.Duration `json:"cleanup_interval"`
	HealthCheckInterval   time.Duration `json:"health_check_interval"`
	MaxMessageSize        int           `json:"max_message_size"`
	BufferSize            int           `json:"buffer_size"`
	EnableCompression     bool          `json:"enable_compression"`
	RateLimitPerSecond    int           `json:"rate_limit_per_second"`
	EnableMetrics         bool          `json:"enable_metrics"`
}

// DefaultConfig 默认配置
func DefaultConfig() *ManagerConfig {
	return &ManagerConfig{
		MaxConnections:        1000,
		MaxConnectionsPerUser: 10,
		HeartbeatInterval:     30 * time.Second,
		ConnectionTimeout:     5 * time.Minute,
		CleanupInterval:       1 * time.Minute,
		HealthCheckInterval:   30 * time.Second,
		MaxMessageSize:        1024 * 1024, // 1MB
		BufferSize:            256,
		EnableCompression:     true,
		RateLimitPerSecond:    100,
		EnableMetrics:         true,
	}
}

// NewUnifiedSSEManager 创建新的统一SSE管理器
func NewUnifiedSSEManager(config *ManagerConfig) *UnifiedSSEManager {
	if config == nil {
		config = DefaultConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &UnifiedSSEManager{
		config:        config,
		ctx:           ctx,
		cancel:        cancel,
		eventHandlers: make(map[ConnectionEvent][]ConnectionEventHandler),
	}

	// 初始化组件
	manager.connectionPool = NewConnectionPool(config)
	manager.messageRouter = NewMessageRouter()
	manager.monitor = NewMonitor(config)

	return manager
}

// Start 启动SSE管理器
func (m *UnifiedSSEManager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("SSE管理器已经在运行")
	}

	global.LOG.Info("🚀 启动统一SSE管理器")

	// 启动组件
	if err := m.monitor.Start(); err != nil {
		return fmt.Errorf("启动监控器失败: %v", err)
	}

	// 启动清理任务
	m.startCleanupTasks()

	m.running = true
	m.startTime = time.Now()

	global.LOG.Info("✅ 统一SSE管理器启动成功")
	return nil
}

// Stop 停止SSE管理器
func (m *UnifiedSSEManager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	global.LOG.Info("🛑 停止统一SSE管理器")

	// 停止清理任务
	m.stopCleanupTasks()

	// 关闭所有连接
	connections := m.connectionPool.GetAll()
	for _, conn := range connections {
		if err := conn.Close(); err != nil {
			global.LOG.Error("关闭连接失败", zap.Error(err), zap.String("connectionID", conn.ID()))
		}
	}

	// 停止组件
	if err := m.monitor.Stop(); err != nil {
		global.LOG.Error("停止监控器失败", zap.Error(err))
	}

	// 取消上下文
	m.cancel()

	m.running = false

	global.LOG.Info("✅ 统一SSE管理器已停止")
	return nil
}

// IsRunning 检查是否正在运行
func (m *UnifiedSSEManager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.running
}

// CreateConnection 创建新的SSE连接
func (m *UnifiedSSEManager) CreateConnection(ctx context.Context, userID uint, w gin.ResponseWriter, r *http.Request, options *ConnectionOptions) (*Connection, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, &SSEError{
			Code:    ErrCodeInternalError,
			Message: "SSE管理器未运行",
		}
	}

	// 检查连接数限制
	if m.connectionPool.Size() >= m.config.MaxConnections {
		return nil, &SSEError{
			Code:    ErrCodeRateLimitExceeded,
			Message: "连接数已达到上限",
		}
	}

	// 检查用户连接数限制
	userConnections := m.connectionPool.GetByUser(userID)
	if len(userConnections) >= m.config.MaxConnectionsPerUser {
		return nil, &SSEError{
			Code:    ErrCodeRateLimitExceeded,
			Message: "用户连接数已达到上限",
		}
	}

	// 合并连接选项
	if options == nil {
		options = &ConnectionOptions{}
	}
	m.mergeConnectionOptions(options)

	// 创建连接
	conn, err := NewSSEConnection(ctx, userID, w, r, options, m)
	if err != nil {
		return nil, err
	}

	// 添加到连接池
	if err := m.connectionPool.Add(conn); err != nil {
		conn.Close()
		return nil, err
	}

	// 记录监控指标
	m.monitor.RecordConnection(userID)

	// 触发连接事件
	m.triggerEvent(conn, EventConnected)

	global.LOG.Info("✅ 创建SSE连接成功",
		zap.String("connectionID", conn.ID()),
		zap.Uint("userID", userID),
		zap.String("remoteAddr", conn.RemoteAddr()))

	return conn, nil
}

// RemoveConnection 移除连接
func (m *UnifiedSSEManager) RemoveConnection(connectionID string) error {
	conn, exists := m.connectionPool.Get(connectionID)
	if !exists {
		return &SSEError{
			Code:    ErrCodeConnectionNotFound,
			Message: "连接不存在",
		}
	}

	// 从连接池移除
	if err := m.connectionPool.Remove(connectionID); err != nil {
		return err
	}

	// 记录监控指标
	m.monitor.RecordDisconnection(conn.UserID())

	// 触发断开事件
	m.triggerEvent(conn, EventDisconnected)

	global.LOG.Info("🔌 移除SSE连接",
		zap.String("connectionID", connectionID),
		zap.Uint("userID", conn.UserID()))

	return nil
}

// GetConnection 获取连接
func (m *UnifiedSSEManager) GetConnection(connectionID string) (*Connection, bool) {
	return m.connectionPool.Get(connectionID)
}

// GetUserConnections 获取用户的所有连接
func (m *UnifiedSSEManager) GetUserConnections(userID uint) []*Connection {
	return m.connectionPool.GetByUser(userID)
}

// GetConnectionCount 获取连接数
func (m *UnifiedSSEManager) GetConnectionCount() int {
	return m.connectionPool.Size()
}

// Subscribe 订阅主题
func (m *UnifiedSSEManager) Subscribe(connectionID string, topics ...string) error {
	conn, exists := m.connectionPool.Get(connectionID)
	if !exists {
		return &SSEError{
			Code:    ErrCodeConnectionNotFound,
			Message: "连接不存在",
		}
	}

	return conn.Subscribe(topics...)
}

// Unsubscribe 取消订阅主题
func (m *UnifiedSSEManager) Unsubscribe(connectionID string, topics ...string) error {
	conn, exists := m.connectionPool.Get(connectionID)
	if !exists {
		return &SSEError{
			Code:    ErrCodeConnectionNotFound,
			Message: "连接不存在",
		}
	}

	return conn.Unsubscribe(topics...)
}

// GetSubscriptions 获取连接的订阅
func (m *UnifiedSSEManager) GetSubscriptions(connectionID string) []string {
	conn, exists := m.connectionPool.Get(connectionID)
	if !exists {
		return nil
	}

	return conn.GetSubscriptions()
}

// 合并连接选项
func (m *UnifiedSSEManager) mergeConnectionOptions(options *ConnectionOptions) {
	if options.MaxIdleTime == 0 {
		options.MaxIdleTime = m.config.ConnectionTimeout
	}
	if options.HeartbeatInterval == 0 {
		options.HeartbeatInterval = m.config.HeartbeatInterval
	}
	if options.BufferSize == 0 {
		options.BufferSize = m.config.BufferSize
	}
	if options.MaxMessageSize == 0 {
		options.MaxMessageSize = m.config.MaxMessageSize
	}
}

// 启动清理任务
func (m *UnifiedSSEManager) startCleanupTasks() {
	// 连接清理任务
	m.cleanupTicker = time.NewTicker(m.config.CleanupInterval)
	go m.cleanupLoop()

	// 健康检查任务
	m.healthTicker = time.NewTicker(m.config.HealthCheckInterval)
	go m.healthCheckLoop()
}

// 停止清理任务
func (m *UnifiedSSEManager) stopCleanupTasks() {
	if m.cleanupTicker != nil {
		m.cleanupTicker.Stop()
	}
	if m.healthTicker != nil {
		m.healthTicker.Stop()
	}
}

// 清理循环
func (m *UnifiedSSEManager) cleanupLoop() {
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-m.cleanupTicker.C:
			m.performCleanup()
		}
	}
}

// 健康检查循环
func (m *UnifiedSSEManager) healthCheckLoop() {
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-m.healthTicker.C:
			m.performHealthCheck()
		}
	}
}

// 执行清理
func (m *UnifiedSSEManager) performCleanup() {
	if err := m.connectionPool.CleanupIdle(m.config.ConnectionTimeout); err != nil {
		global.LOG.Error("清理空闲连接失败", zap.Error(err))
	}
}

// 执行健康检查
func (m *UnifiedSSEManager) performHealthCheck() {
	status := m.monitor.HealthCheck()
	if status.Status != "healthy" {
		global.LOG.Warn("SSE管理器健康检查异常",
			zap.String("status", status.Status),
			zap.Any("details", status.Details))
	}
}

// 触发事件
func (m *UnifiedSSEManager) triggerEvent(conn Connection, event ConnectionEvent) {
	handlers, exists := m.eventHandlers[event]
	if !exists {
		return
	}

	for _, handler := range handlers {
		go func(h ConnectionEventHandler) {
			defer func() {
				if r := recover(); r != nil {
					global.LOG.Error("事件处理器panic", zap.Any("panic", r))
				}
			}()
			h(conn, event)
		}(handler)
	}
}

// SendToConnection 发送消息到指定连接
func (m *UnifiedSSEManager) SendToConnection(connectionID string, message *Message) error {
	conn, exists := m.connectionPool.Get(connectionID)
	if !exists {
		return &SSEError{
			Code:    ErrCodeConnectionNotFound,
			Message: "连接不存在",
		}
	}

	// 记录监控指标
	m.monitor.RecordMessage(message.Topic, len(fmt.Sprintf("%v", message.Data)))

	return conn.Send(message)
}

// SendToUser 发送消息到用户的所有连接
func (m *UnifiedSSEManager) SendToUser(userID uint, message *Message) error {
	connections := m.connectionPool.GetByUser(userID)
	if len(connections) == 0 {
		return &SSEError{
			Code:    ErrCodeConnectionNotFound,
			Message: "用户没有活跃连接",
		}
	}

	var lastErr error
	successCount := 0

	for _, conn := range connections {
		if err := conn.Send(message); err != nil {
			lastErr = err
			global.LOG.Error("发送消息到用户连接失败",
				zap.Error(err),
				zap.String("connectionID", conn.ID()),
				zap.Uint("userID", userID))
		} else {
			successCount++
		}
	}

	// 记录监控指标
	m.monitor.RecordMessage(message.Topic, len(fmt.Sprintf("%v", message.Data)))

	if successCount == 0 && lastErr != nil {
		return lastErr
	}

	return nil
}

// SendToTopic 发送消息到主题的所有订阅者
func (m *UnifiedSSEManager) SendToTopic(topic string, message *Message) error {
	connections := m.connectionPool.GetAll()
	if len(connections) == 0 {
		return nil
	}

	message.Topic = topic
	successCount := 0

	for _, conn := range connections {
		subscriptions := conn.GetSubscriptions()
		subscribed := false
		for _, sub := range subscriptions {
			if sub == topic {
				subscribed = true
				break
			}
		}

		if subscribed {
			if err := conn.Send(message); err != nil {
				global.LOG.Error("发送主题消息失败",
					zap.Error(err),
					zap.String("connectionID", conn.ID()),
					zap.String("topic", topic))
			} else {
				successCount++
			}
		}
	}

	// 记录监控指标
	m.monitor.RecordMessage(topic, len(fmt.Sprintf("%v", message.Data)))

	global.LOG.Debug("主题消息发送完成",
		zap.String("topic", topic),
		zap.Int("successCount", successCount))

	return nil
}

// Broadcast 广播消息到所有连接
func (m *UnifiedSSEManager) Broadcast(message *Message) error {
	connections := m.connectionPool.GetAll()
	if len(connections) == 0 {
		return nil
	}

	successCount := 0

	for _, conn := range connections {
		if err := conn.Send(message); err != nil {
			global.LOG.Error("广播消息失败",
				zap.Error(err),
				zap.String("connectionID", conn.ID()))
		} else {
			successCount++
		}
	}

	// 记录监控指标
	m.monitor.RecordMessage("broadcast", len(fmt.Sprintf("%v", message.Data)))

	global.LOG.Debug("广播消息发送完成",
		zap.Int("successCount", successCount))

	return nil
}

// GetStats 获取统计信息
func (m *UnifiedSSEManager) GetStats() *Stats {
	stats := m.monitor.GetStats()
	stats.ActiveConnections = int64(m.connectionPool.ActiveCount())
	stats.TotalConnections = int64(m.connectionPool.Size())

	if m.running {
		stats.Uptime = time.Since(m.startTime)
	}

	return stats
}

// GetHealthStatus 获取健康状态
func (m *UnifiedSSEManager) GetHealthStatus() *HealthStatus {
	return m.monitor.HealthCheck()
}

// OnConnectionEvent 注册连接事件处理器
func (m *UnifiedSSEManager) OnConnectionEvent(event ConnectionEvent, handler ConnectionEventHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.eventHandlers[event] == nil {
		m.eventHandlers[event] = make([]ConnectionEventHandler, 0)
	}
	m.eventHandlers[event] = append(m.eventHandlers[event], handler)
}
