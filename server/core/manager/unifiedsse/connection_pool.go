package unifiedsse

import (
	"sync"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// ConnectionPoolImpl 连接池实现
type ConnectionPoolImpl struct {
	// 连接存储
	connections map[string]*SSEConnection
	userIndex   map[uint][]*SSEConnection
	mu          sync.RWMutex

	// 配置
	config *ManagerConfig

	// 统计信息
	totalConnections int
	activeConnections int
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(config *ManagerConfig) ConnectionPool {
	return &ConnectionPoolImpl{
		connections: make(map[string]*SSEConnection),
		userIndex:   make(map[uint][]*SSEConnection),
		config:      config,
	}
}

// Add 添加连接
func (p *ConnectionPoolImpl) Add(conn Connection) error {
	sseConn, ok := conn.(*SSEConnection)
	if !ok {
		return &SSEError{
			Code:    ErrCodeInternalError,
			Message: "无效的连接类型",
		}
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查连接是否已存在
	if _, exists := p.connections[sseConn.ID()]; exists {
		return &SSEError{
			Code:    ErrCodeInternalError,
			Message: "连接已存在",
		}
	}

	// 添加到连接映射
	p.connections[sseConn.ID()] = sseConn

	// 添加到用户索引
	userID := sseConn.UserID()
	if p.userIndex[userID] == nil {
		p.userIndex[userID] = make([]*SSEConnection, 0)
	}
	p.userIndex[userID] = append(p.userIndex[userID], sseConn)

	// 更新统计
	p.totalConnections++
	p.activeConnections++

	global.LOG.Debug("连接已添加到连接池",
		zap.String("connectionID", sseConn.ID()),
		zap.Uint("userID", userID),
		zap.Int("totalConnections", p.totalConnections))

	return nil
}

// Remove 移除连接
func (p *ConnectionPoolImpl) Remove(connectionID string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	conn, exists := p.connections[connectionID]
	if !exists {
		return &SSEError{
			Code:    ErrCodeConnectionNotFound,
			Message: "连接不存在",
		}
	}

	// 从连接映射移除
	delete(p.connections, connectionID)

	// 从用户索引移除
	userID := conn.UserID()
	userConnections := p.userIndex[userID]
	for i, userConn := range userConnections {
		if userConn.ID() == connectionID {
			// 移除该连接
			p.userIndex[userID] = append(userConnections[:i], userConnections[i+1:]...)
			break
		}
	}

	// 如果用户没有其他连接，删除用户索引
	if len(p.userIndex[userID]) == 0 {
		delete(p.userIndex, userID)
	}

	// 更新统计
	p.activeConnections--

	global.LOG.Debug("连接已从连接池移除",
		zap.String("connectionID", connectionID),
		zap.Uint("userID", userID),
		zap.Int("activeConnections", p.activeConnections))

	return nil
}

// Get 获取连接
func (p *ConnectionPoolImpl) Get(connectionID string) (*Connection, bool) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	conn, exists := p.connections[connectionID]
	if !exists {
		return nil, false
	}

	// 转换为接口类型
	var connInterface Connection = conn
	return &connInterface, true
}

// GetByUser 获取用户的所有连接
func (p *ConnectionPoolImpl) GetByUser(userID uint) []*Connection {
	p.mu.RLock()
	defer p.mu.RUnlock()

	userConnections := p.userIndex[userID]
	if len(userConnections) == 0 {
		return nil
	}

	// 转换为接口类型
	connections := make([]*Connection, len(userConnections))
	for i, conn := range userConnections {
		var connInterface Connection = conn
		connections[i] = &connInterface
	}

	return connections
}

// GetAll 获取所有连接
func (p *ConnectionPoolImpl) GetAll() []*Connection {
	p.mu.RLock()
	defer p.mu.RUnlock()

	connections := make([]*Connection, 0, len(p.connections))
	for _, conn := range p.connections {
		var connInterface Connection = conn
		connections = append(connections, &connInterface)
	}

	return connections
}

// Size 获取连接池大小
func (p *ConnectionPoolImpl) Size() int {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return len(p.connections)
}

// ActiveCount 获取活跃连接数
func (p *ConnectionPoolImpl) ActiveCount() int {
	p.mu.RLock()
	defer p.mu.RUnlock()

	activeCount := 0
	for _, conn := range p.connections {
		if conn.IsActive() {
			activeCount++
		}
	}

	return activeCount
}

// IdleCount 获取空闲连接数
func (p *ConnectionPoolImpl) IdleCount() int {
	p.mu.RLock()
	defer p.mu.RUnlock()

	idleCount := 0
	for _, conn := range p.connections {
		if !conn.IsActive() {
			idleCount++
		}
	}

	return idleCount
}

// Cleanup 清理所有连接
func (p *ConnectionPoolImpl) Cleanup() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	for connectionID, conn := range p.connections {
		if err := conn.Close(); err != nil {
			global.LOG.Error("关闭连接失败",
				zap.Error(err),
				zap.String("connectionID", connectionID))
		}
	}

	// 清空所有映射
	p.connections = make(map[string]*SSEConnection)
	p.userIndex = make(map[uint][]*SSEConnection)
	p.activeConnections = 0

	global.LOG.Info("连接池已清理", zap.Int("totalConnections", p.totalConnections))

	return nil
}

// CleanupIdle 清理空闲连接
func (p *ConnectionPoolImpl) CleanupIdle(maxIdleTime time.Duration) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	now := time.Now()
	toRemove := make([]string, 0)

	for connectionID, conn := range p.connections {
		// 检查连接是否已关闭或超时
		if !conn.IsActive() || now.Sub(conn.LastActivity()) > maxIdleTime {
			toRemove = append(toRemove, connectionID)
		}
	}

	// 移除空闲连接
	for _, connectionID := range toRemove {
		conn := p.connections[connectionID]
		if err := conn.Close(); err != nil {
			global.LOG.Error("关闭空闲连接失败",
				zap.Error(err),
				zap.String("connectionID", connectionID))
		}

		// 从映射中移除
		delete(p.connections, connectionID)

		// 从用户索引移除
		userID := conn.UserID()
		userConnections := p.userIndex[userID]
		for i, userConn := range userConnections {
			if userConn.ID() == connectionID {
				p.userIndex[userID] = append(userConnections[:i], userConnections[i+1:]...)
				break
			}
		}

		// 如果用户没有其他连接，删除用户索引
		if len(p.userIndex[userID]) == 0 {
			delete(p.userIndex, userID)
		}

		p.activeConnections--
	}

	if len(toRemove) > 0 {
		global.LOG.Info("清理空闲连接完成",
			zap.Int("removedCount", len(toRemove)),
			zap.Int("activeConnections", p.activeConnections))
	}

	return nil
}

// GetUserConnectionCount 获取用户连接数
func (p *ConnectionPoolImpl) GetUserConnectionCount(userID uint) int {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return len(p.userIndex[userID])
}

// GetConnectionsByStatus 根据状态获取连接
func (p *ConnectionPoolImpl) GetConnectionsByStatus(active bool) []*Connection {
	p.mu.RLock()
	defer p.mu.RUnlock()

	connections := make([]*Connection, 0)
	for _, conn := range p.connections {
		if conn.IsActive() == active {
			var connInterface Connection = conn
			connections = append(connections, &connInterface)
		}
	}

	return connections
}
