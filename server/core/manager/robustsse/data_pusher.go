package robustsse

import (
	"context"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// DataPusher 数据推送服务
type DataPusher struct {
	manager *RobustSSEManager
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewDataPusher 创建数据推送服务
func NewDataPusher() *DataPusher {
	ctx, cancel := context.WithCancel(context.Background())
	return &DataPusher{
		manager: GetRobustSSEManager(),
		ctx:     ctx,
		cancel:  cancel,
	}
}

// Start 启动数据推送服务
func (dp *DataPusher) Start() {
	global.LOG.Info("🚀 启动数据推送服务")

	// 启动各种数据推送器
	go dp.startDashboardPusher()
	go dp.startSystemInfoPusher()
	go dp.startNetworkTopologyPusher()
	go dp.startCleanupService()

	global.LOG.Info("✅ 所有数据推送器已启动")
}

// Stop 停止数据推送服务
func (dp *DataPusher) Stop() {
	global.LOG.Info("🛑 停止数据推送服务")
	dp.cancel()
}

// startDashboardPusher 启动仪表盘数据推送器
func (dp *DataPusher) startDashboardPusher() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒推送一次
	defer ticker.Stop()

	global.LOG.Info("📊 仪表盘数据推送器已启动")

	for {
		select {
		case <-dp.ctx.Done():
			global.LOG.Info("📊 仪表盘数据推送器已停止")
			return
		case <-ticker.C:
			dp.pushDashboardStats()
		}
	}
}

// startSystemInfoPusher 启动系统信息推送器
func (dp *DataPusher) startSystemInfoPusher() {
	ticker := time.NewTicker(3 * time.Second) // 每3秒推送一次
	defer ticker.Stop()

	global.LOG.Info("💻 系统信息推送器已启动")

	for {
		select {
		case <-dp.ctx.Done():
			global.LOG.Info("💻 系统信息推送器已停止")
			return
		case <-ticker.C:
			dp.pushSystemInfo()
		}
	}
}

// startNetworkTopologyPusher 启动网络拓扑推送器
func (dp *DataPusher) startNetworkTopologyPusher() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒推送一次
	defer ticker.Stop()

	global.LOG.Info("🌐 网络拓扑推送器已启动")

	for {
		select {
		case <-dp.ctx.Done():
			global.LOG.Info("🌐 网络拓扑推送器已停止")
			return
		case <-ticker.C:
			dp.pushNetworkTopology()
		}
	}
}

// startCleanupService 启动清理服务
func (dp *DataPusher) startCleanupService() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟清理一次
	defer ticker.Stop()

	global.LOG.Info("🧹 连接清理服务已启动")

	for {
		select {
		case <-dp.ctx.Done():
			global.LOG.Info("🧹 连接清理服务已停止")
			return
		case <-ticker.C:
			dp.manager.CleanupInactiveConnections()
		}
	}
}

// pushDashboardStats 推送仪表盘统计数据
func (dp *DataPusher) pushDashboardStats() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送仪表盘数据时发生panic", zap.Any("panic", r))
		}
	}()

	stats, err := dp.dashboardService.GetDashboardStats()
	if err != nil {
		global.LOG.Debug("获取仪表盘统计数据失败", zap.Error(err))
		return
	}

	dp.manager.BroadcastMessage("dashboard", "stats", stats)
}

// pushSystemInfo 推送系统信息
func (dp *DataPusher) pushSystemInfo() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送系统信息时发生panic", zap.Any("panic", r))
		}
	}()

	systemInfo, err := dp.dashboardService.GetSystemInfo()
	if err != nil {
		global.LOG.Debug("获取系统信息失败", zap.Error(err))
		return
	}

	dp.manager.BroadcastMessage("dashboard", "system_info", systemInfo)
}

// pushNetworkTopology 推送网络拓扑数据
func (dp *DataPusher) pushNetworkTopology() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送网络拓扑数据时发生panic", zap.Any("panic", r))
		}
	}()

	topology, err := dp.dashboardService.GetNetworkTopology()
	if err != nil {
		global.LOG.Debug("获取网络拓扑数据失败", zap.Error(err))
		return
	}

	dp.manager.BroadcastMessage("dashboard", "topology", topology)
}

// PushNotification 推送通知消息
func (dp *DataPusher) PushNotification(userID uint, notification interface{}) {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送通知消息时发生panic", zap.Any("panic", r))
		}
	}()

	if userID == 0 {
		// 广播通知
		dp.manager.BroadcastMessage("notification", "message", notification)
	} else {
		// 发送给特定用户
		dp.manager.SendMessageToUser(userID, "notification", "message", notification)
	}
}

// PushClientUpdate 推送客户端更新
func (dp *DataPusher) PushClientUpdate(clientData interface{}) {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送客户端更新时发生panic", zap.Any("panic", r))
		}
	}()

	dp.manager.BroadcastMessage("client", "update", clientData)
}

// PushNetworkProgress 推送网络进度
func (dp *DataPusher) PushNetworkProgress(userID uint, progress interface{}) {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("推送网络进度时发生panic", zap.Any("panic", r))
		}
	}()

	if userID == 0 {
		dp.manager.BroadcastMessage("network", "progress", progress)
	} else {
		dp.manager.SendMessageToUser(userID, "network", "progress", progress)
	}
}

var globalDataPusher *DataPusher

// GetDataPusher 获取全局数据推送器
func GetDataPusher() *DataPusher {
	if globalDataPusher == nil {
		globalDataPusher = NewDataPusher()
	}
	return globalDataPusher
}

// InitDataPusher 初始化数据推送器
func InitDataPusher() {
	pusher := GetDataPusher()
	pusher.Start()
	global.LOG.Info("✅ 数据推送器初始化完成")
}
