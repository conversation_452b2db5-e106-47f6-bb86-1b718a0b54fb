package sys

import (
	"time"

	"server/core/manager/ssemgr"
	"server/global"
	"server/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DashboardApi struct{}

// GetSystemInfo 获取系统信息
func (i *DashboardApi) GetSystemInfo(c *gin.Context) {
	server, err := dashboardService.GetSystemInfo()
	if err != nil {
		global.LOG.Error("获取系统信息失败", zap.Error(err))
		response.ErrorWithMessage("获取系统信息失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"server": server}, "获取成功", c)
}

// GetDashboardStats 获取dashboard统计信息
func (i *DashboardApi) GetDashboardStats(c *gin.Context) {
	stats, err := dashboardService.GetDashboardStats()
	if err != nil {
		global.LOG.Error("获取dashboard统计信息失败", zap.Error(err))
		response.ErrorWithMessage("获取dashboard统计信息失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"stats": stats}, "获取成功", c)
}

// DashboardSSE 仪表盘SSE连接 (已弃用，请使用 /sse/dashboard)
// @Tags 仪表盘
// @Summary 仪表盘SSE连接
// @Security ApiKeyAuth
// @accept text/event-stream
// @Produce text/event-stream
// @Success 200 {string} string "SSE连接成功"
// @Router /dashboard/sse [get]
// @Deprecated
func (i *DashboardApi) DashboardSSE(c *gin.Context) {
	// 使用新的统一SSE处理器
	handler := ssemgr.NewHandler()
	handler.HandleDashboardSSE(c)
}

// 发送系统信息
func (i *DashboardApi) sendSystemInfo(c *gin.Context) {
	server, err := dashboardService.GetSystemInfo()
	if err != nil {
		global.LOG.Error("获取系统信息失败", zap.Error(err))
		return
	}

	// 发送SSE数据
	c.SSEvent("system_info", gin.H{
		"type":      "system_info",
		"data":      server,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()
}

// 发送统计信息
func (i *DashboardApi) sendDashboardStats(c *gin.Context) {
	stats, err := dashboardService.GetDashboardStats()
	if err != nil {
		global.LOG.Error("获取dashboard统计信息失败", zap.Error(err))
		return
	}

	// 发送SSE数据
	c.SSEvent("dashboard_stats", gin.H{
		"type":      "dashboard_stats",
		"data":      stats,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()
}

// 🌐 新增：获取网络拓扑图数据
func (i *DashboardApi) GetNetworkTopology(c *gin.Context) {
	topology, err := dashboardService.GetNetworkTopology()
	if err != nil {
		global.LOG.Error("获取网络拓扑数据失败", zap.Error(err))
		response.ErrorWithMessage("获取网络拓扑数据失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"topology": topology}, "获取成功", c)
}

// 发送拓扑数据
func (i *DashboardApi) sendTopologyData(c *gin.Context) {
	topology, err := dashboardService.GetNetworkTopology()
	if err != nil {
		global.LOG.Error("获取拓扑数据失败", zap.Error(err))
		return
	}

	// 发送SSE数据
	c.SSEvent("topology_data", gin.H{
		"type": "topology_data",
		"data": topology,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()
}
