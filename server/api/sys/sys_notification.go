package sys

import (
	"server/core/manager/ssemgr"
	"server/global"
	"server/model/request"
	"server/model/response"
	"server/service/sys"
	"server/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type NotificationApi struct{}

// GetNotificationList 获取通知列表
// @Tags 系统通知
// @Summary 获取通知列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.NotificationListRequest true "分页参数"
// @Success 200 {object} response.Response{data=response.NotificationListResponse,msg=string} "获取成功"
// @Router /notification/list [get]
func (n *NotificationApi) GetNotificationList(c *gin.Context) {
	var req request.NotificationListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		global.LOG.Error("❌ 通知列表请求参数错误", zap.Error(err))
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	global.LOG.Info("📋 获取通知列表请求",
		zap.Uint("user_id", userID),
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize))

	notificationService := sys.NotificationService{}
	list, err := notificationService.GetNotificationList(userID, req)
	if err != nil {
		global.LOG.Error("❌ 获取通知列表失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		response.ErrorWithMessage("获取通知列表失败", c)
		return
	}

	global.LOG.Info("✅ 通知列表获取成功",
		zap.Uint("user_id", userID),
		zap.Int("total", len(list.List)),
		zap.Int64("total_count", list.Total))

	response.OkWithDetailed(list, "获取成功", c)
}

// GetNotificationStats 获取通知统计信息
// @Tags 系统通知
// @Summary 获取通知统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.NotificationStatsResponse,msg=string} "获取成功"
// @Router /notification/stats [get]
func (n *NotificationApi) GetNotificationStats(c *gin.Context) {
	userID := utils.GetUserID(c)

	notificationService := sys.NotificationService{}
	stats, err := notificationService.GetNotificationStats(userID)
	if err != nil {
		global.LOG.Error("获取通知统计失败", zap.Error(err))
		response.ErrorWithMessage("获取通知统计失败", c)
		return
	}

	response.OkWithDetailed(response.NotificationStatsResponse{
		Total:  stats.Total,
		Unread: stats.Unread,
		Read:   stats.Read,
	}, "获取成功", c)
}

// MarkAsRead 标记通知为已读
// @Tags 系统通知
// @Summary 标记通知为已读
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.NotificationMarkReadRequest true "通知ID列表"
// @Success 200 {object} response.Response{msg=string} "标记成功"
// @Router /notification/read [post]
func (n *NotificationApi) MarkAsRead(c *gin.Context) {
	var req request.NotificationMarkReadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)

	notificationService := sys.NotificationService{}
	if err := notificationService.MarkAsRead(userID, req); err != nil {
		global.LOG.Error("标记通知已读失败", zap.Error(err))
		response.ErrorWithMessage("标记通知已读失败", c)
		return
	}

	response.OkWithMessage("标记成功", c)
}

// MarkAllAsRead 标记所有通知为已读
// @Tags 系统通知
// @Summary 标记所有通知为已读
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.NotificationMarkAllReadRequest true "过滤条件"
// @Success 200 {object} response.Response{msg=string} "标记成功"
// @Router /notification/read-all [post]
func (n *NotificationApi) MarkAllAsRead(c *gin.Context) {
	var req request.NotificationMarkAllReadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)

	notificationService := sys.NotificationService{}
	if err := notificationService.MarkAllAsRead(userID, req); err != nil {
		global.LOG.Error("标记所有通知已读失败", zap.Error(err))
		response.ErrorWithMessage("标记所有通知已读失败", c)
		return
	}

	response.OkWithMessage("标记成功", c)
}

// DeleteNotifications 删除通知
// @Tags 系统通知
// @Summary 删除通知
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.NotificationDeleteRequest true "通知ID列表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /notification/delete [delete]
func (n *NotificationApi) DeleteNotifications(c *gin.Context) {
	var req request.NotificationDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)

	notificationService := sys.NotificationService{}
	if err := notificationService.DeleteNotifications(userID, req); err != nil {
		global.LOG.Error("删除通知失败", zap.Error(err))
		response.ErrorWithMessage("删除通知失败", c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// SSEConnect SSE连接端点 (已弃用，请使用 /sse/notification)
// @Tags 系统通知
// @Summary SSE连接
// @Security ApiKeyAuth
// @accept text/event-stream
// @Produce text/event-stream
// @Success 200 {string} string "SSE连接成功"
// @Router /notification/sse [get]
// @Deprecated
func (n *NotificationApi) SSEConnect(c *gin.Context) {
	// 使用新的统一SSE处理器
	handler := ssemgr.NewHandler()
	handler.HandleNotificationSSE(c)
}

// GetSSEStats 获取SSE连接统计 (已弃用，请使用 /sse/stats)
// @Tags 系统通知
// @Summary 获取SSE连接统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /notification/sse/stats [get]
// @Deprecated
func (n *NotificationApi) GetSSEStats(c *gin.Context) {
	// 使用新的统一SSE处理器
	handler := ssemgr.NewHandler()
	handler.GetSSEStats(c)
}

// CleanupNotifications 清理旧通知
// @Tags 系统通知
// @Summary 清理旧通知
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param keepCount query int false "保留通知数量" default(100)
// @Success 200 {object} response.Response{msg=string} "清理成功"
// @Router /notification/cleanup [post]
func (n *NotificationApi) CleanupNotifications(c *gin.Context) {
	userID := utils.GetUserID(c)

	// 获取保留数量参数
	keepCountStr := c.DefaultQuery("keepCount", "100")
	keepCount, err := strconv.Atoi(keepCountStr)
	if err != nil || keepCount <= 0 {
		keepCount = 100
	}

	notificationService := sys.NotificationService{}
	if err := notificationService.CleanupOldNotifications(userID, keepCount); err != nil {
		global.LOG.Error("清理通知失败", zap.Error(err))
		response.ErrorWithMessage("清理通知失败", c)
		return
	}

	response.OkWithMessage("清理成功", c)
}
