package c2

import (
	"server/core/manager/ssemgr"
	"server/global"
	"server/model/request/network"
	"server/model/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type NetworkApi struct{}

// GetNetworkStats 获取网络统计信息
func (n *NetworkApi) GetNetworkStats(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	var req network.NetworkStatsRequest
	taskID, err := networkService.GetNetworkStats(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取网络统计信息失败", zap.Error(err))
		response.ErrorWithMessage("获取网络统计信息失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应（增加超时时间）
	waitForResponseAsyncWithClientId(c, taskID, "获取网络统计信息", clientIDStr)
}

// GetNetworkInterfaces 获取网络接口信息
func (n *NetworkApi) GetNetworkInterfaces(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	var req network.NetworkInterfacesRequest
	taskID, err := networkService.GetNetworkInterfaces(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取网络接口信息失败", zap.Error(err))
		response.ErrorWithMessage("获取网络接口信息失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应（网络接口信息获取可能需要更长时间）
	waitForResponseAsyncWithClientId(c, taskID, "获取网络接口信息", clientIDStr)
}

// GetNetworkInterfaceProgressStream 获取网络接口处理进度流 (已弃用，请使用 /sse/network)
// @Tags 网络管理
// @Summary 获取网络接口处理进度流
// @Security ApiKeyAuth
// @accept text/event-stream
// @Produce text/event-stream
// @Param clientId path string true "客户端ID"
// @Success 200 {string} string "SSE连接成功"
// @Router /network/{clientId}/interfaces/progress-stream [get]
// @Deprecated
func (n *NetworkApi) GetNetworkInterfaceProgressStream(c *gin.Context) {
	// 使用新的统一SSE处理器
	handler := ssemgr.NewHandler()
	handler.HandleNetworkSSE(c)
}

// GetNetworkConnections 获取网络连接信息
func (n *NetworkApi) GetNetworkConnections(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	protocol := c.Query("protocol")
	state := c.Query("state")

	req := network.NetworkConnectionsRequest{
		Protocol: protocol,
		State:    state,
	}

	taskID, err := networkService.GetNetworkConnections(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取网络连接信息失败", zap.Error(err))
		response.ErrorWithMessage("获取网络连接信息失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(c, taskID, "获取网络连接信息", clientIDStr)
}

// CloseConnection 关闭网络连接
func (n *NetworkApi) CloseConnection(c *gin.Context) {
	clientIDStr := c.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", c)
		return
	}

	var req network.CloseConnectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	taskID, err := networkService.CloseConnection(uint(clientID), req)
	if err != nil {
		global.LOG.Error("关闭网络连接失败", zap.Error(err))
		response.ErrorWithMessage("关闭网络连接失败: "+err.Error(), c)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(c, taskID, "关闭网络连接", clientIDStr)
}
