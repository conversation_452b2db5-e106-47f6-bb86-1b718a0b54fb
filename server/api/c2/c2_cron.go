package c2

import (
	"server/global"
	cronreq "server/model/request/cron"
	"server/model/response"
	"server/service"
	"server/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CronApi struct{}

// CreateTask 创建定时任务
// @Tags 定时任务管理
// @Summary 创建定时任务
// @Description 创建一个新的定时任务
// @Accept json
// @Produce json
// @Param data body cronreq.CreateCronTaskRequest true "创建任务请求"
// @Success 200 {object} response.Response{data=task.CronTask} "创建成功"
// @Router /api/cron/tasks [post]
func (a *CronApi) CreateTask(c *gin.Context) {
	var req cronreq.CreateCronTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 创建任务
	cronTask, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.CreateTask(req, userID)
	if err != nil {
		global.LOG.Error("创建定时任务失败", zap.Error(err))
		response.ErrorWithMessage("创建任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(cronTask, c)
}

// UpdateTask 更新定时任务
// @Tags 定时任务管理
// @Summary 更新定时任务
// @Description 更新指定的定时任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Param data body cronreq.UpdateCronTaskRequest true "更新任务请求"
// @Success 200 {object} response.Response "更新成功"
// @Router /api/cron/tasks/{id} [put]
func (a *CronApi) UpdateTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	var req cronreq.UpdateCronTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 更新任务
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.UpdateTask(taskID, req); err != nil {
		global.LOG.Error("更新定时任务失败", zap.Error(err))
		response.ErrorWithMessage("更新任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteTask 删除定时任务
// @Tags 定时任务管理
// @Summary 删除定时任务
// @Description 删除指定的定时任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "删除成功"
// @Router /api/cron/tasks/{id} [delete]
func (a *CronApi) DeleteTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	// 删除任务
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.DeleteTask(taskID); err != nil {
		global.LOG.Error("删除定时任务失败", zap.Error(err))
		response.ErrorWithMessage("删除任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetTask 获取任务详情
// @Tags 定时任务管理
// @Summary 获取任务详情
// @Description 获取指定任务的详细信息
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response{data=task.CronTask} "获取成功"
// @Router /api/cron/tasks/{id} [get]
func (a *CronApi) GetTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	// 获取任务
	cronTask, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.GetTask(taskID)
	if err != nil {
		global.LOG.Error("获取定时任务失败", zap.Error(err))
		response.ErrorWithMessage("获取任务失败: "+err.Error(), c)
		return
	}

	response.OkWithData(cronTask, c)
}

// GetTaskList 获取任务列表
// @Tags 定时任务管理
// @Summary 获取任务列表
// @Description 获取定时任务列表，支持分页和筛选
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param client_id query uint false "客户端ID筛选"
// @Param task_type query string false "任务类型筛选"
// @Param status query string false "状态筛选"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} response.Response{data=cronreq.CronTaskListResponse} "获取成功"
// @Router /api/cron/tasks [get]
func (a *CronApi) GetTaskList(c *gin.Context) {
	var req cronreq.CronTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取任务列表
	taskList, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.GetTaskList(req)
	if err != nil {
		global.LOG.Error("获取定时任务列表失败", zap.Error(err))
		response.ErrorWithMessage("获取任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(taskList, c)
}

// StartTask 启动任务
// @Tags 定时任务管理
// @Summary 启动任务
// @Description 启动指定的定时任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "启动成功"
// @Router /api/cron/tasks/{id}/start [post]
func (a *CronApi) StartTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	// 启动任务
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.StartTask(taskID); err != nil {
		global.LOG.Error("启动定时任务失败", zap.Error(err))
		response.ErrorWithMessage("启动任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("启动成功", c)
}

// StopTask 停止任务
// @Tags 定时任务管理
// @Summary 停止任务
// @Description 停止指定的定时任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "停止成功"
// @Router /api/cron/tasks/{id}/stop [post]
func (a *CronApi) StopTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	// 停止任务
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.StopTask(taskID); err != nil {
		global.LOG.Error("停止定时任务失败", zap.Error(err))
		response.ErrorWithMessage("停止任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("停止成功", c)
}

// PauseTask 暂停任务
// @Tags 定时任务管理
// @Summary 暂停任务
// @Description 暂停指定的定时任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "暂停成功"
// @Router /api/cron/tasks/{id}/pause [post]
func (a *CronApi) PauseTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	// 暂停任务
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.PauseTask(taskID); err != nil {
		global.LOG.Error("暂停定时任务失败", zap.Error(err))
		response.ErrorWithMessage("暂停任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("暂停成功", c)
}

// ExecuteTask 立即执行任务
// @Tags 定时任务管理
// @Summary 立即执行任务
// @Description 立即执行指定的定时任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "执行成功"
// @Router /api/cron/tasks/{id}/execute [post]
func (a *CronApi) ExecuteTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的任务ID", c)
		return
	}

	// 立即执行任务
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.ExecuteTask(taskID); err != nil {
		global.LOG.Error("立即执行定时任务失败", zap.Error(err))
		response.ErrorWithMessage("执行任务失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("执行成功", c)
}

// GetExecutionList 获取执行记录列表
// @Tags 定时任务管理
// @Summary 获取执行记录列表
// @Description 获取定时任务执行记录列表，支持分页和筛选
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param task_id query uint64 false "任务ID筛选"
// @Param client_id query uint false "客户端ID筛选"
// @Param status query string false "状态筛选"
// @Success 200 {object} response.Response{data=cronreq.CronExecutionListResponse} "获取成功"
// @Router /api/cron/executions [get]
func (a *CronApi) GetExecutionList(c *gin.Context) {
	var req cronreq.CronExecutionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取执行记录列表
	executionList, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.GetExecutionList(req)
	if err != nil {
		global.LOG.Error("获取执行记录列表失败", zap.Error(err))
		response.ErrorWithMessage("获取执行记录列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(executionList, c)
}

// GetExecution 获取执行记录详情
// @Tags 定时任务管理
// @Summary 获取执行记录详情
// @Description 获取指定执行记录的详细信息
// @Accept json
// @Produce json
// @Param id path uint64 true "执行记录ID"
// @Success 200 {object} response.Response{data=cronreq.CronExecutionResponse} "获取成功"
// @Router /api/cron/executions/{id} [get]
func (a *CronApi) GetExecution(c *gin.Context) {
	executionIDStr := c.Param("id")
	executionID, err := strconv.ParseUint(executionIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的执行记录ID", c)
		return
	}

	// 获取执行记录
	execution, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.GetExecution(executionID)
	if err != nil {
		global.LOG.Error("获取执行记录失败", zap.Error(err))
		response.ErrorWithMessage("获取执行记录失败: "+err.Error(), c)
		return
	}

	response.OkWithData(execution, c)
}

// GetTemplateList 获取任务模板列表
// @Tags 定时任务管理
// @Summary 获取任务模板列表
// @Description 获取所有任务模板列表
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=cronreq.CronTemplateListResponse} "获取成功"
// @Router /api/cron/templates [get]
func (a *CronApi) GetTemplateList(c *gin.Context) {
	// 获取模板列表
	templateList, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.GetTemplateList()
	if err != nil {
		global.LOG.Error("获取任务模板列表失败", zap.Error(err))
		response.ErrorWithMessage("获取模板列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(templateList, c)
}

// CreateTemplate 创建任务模板
// @Tags 定时任务管理
// @Summary 创建任务模板
// @Description 创建一个新的任务模板
// @Accept json
// @Produce json
// @Param data body cronreq.CronTemplateRequest true "创建模板请求"
// @Success 200 {object} response.Response{data=task.CronTemplate} "创建成功"
// @Router /api/cron/templates [post]
func (a *CronApi) CreateTemplate(c *gin.Context) {
	var req cronreq.CronTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 创建模板
	template, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.CreateTemplate(req, userID)
	if err != nil {
		global.LOG.Error("创建任务模板失败", zap.Error(err))
		response.ErrorWithMessage("创建模板失败: "+err.Error(), c)
		return
	}

	response.OkWithData(template, c)
}

// GetTemplate 获取任务模板详情
// @Tags 定时任务管理
// @Summary 获取任务模板详情
// @Description 获取指定任务模板的详细信息
// @Accept json
// @Produce json
// @Param id path uint64 true "模板ID"
// @Success 200 {object} response.Response{data=task.CronTemplate} "获取成功"
// @Router /api/cron/templates/{id} [get]
func (a *CronApi) GetTemplate(c *gin.Context) {
	templateIDStr := c.Param("id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的模板ID", c)
		return
	}

	// 获取模板
	template, err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.GetTemplate(templateID)
	if err != nil {
		global.LOG.Error("获取任务模板失败", zap.Error(err))
		response.ErrorWithMessage("获取模板失败: "+err.Error(), c)
		return
	}

	response.OkWithData(template, c)
}

// UpdateTemplate 更新任务模板
// @Tags 定时任务管理
// @Summary 更新任务模板
// @Description 更新指定的任务模板
// @Accept json
// @Produce json
// @Param id path uint64 true "模板ID"
// @Param data body cronreq.CronTemplateRequest true "更新模板请求"
// @Success 200 {object} response.Response "更新成功"
// @Router /api/cron/templates/{id} [put]
func (a *CronApi) UpdateTemplate(c *gin.Context) {
	templateIDStr := c.Param("id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的模板ID", c)
		return
	}

	var req cronreq.CronTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 更新模板
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.UpdateTemplate(templateID, req, userID); err != nil {
		global.LOG.Error("更新任务模板失败", zap.Error(err))
		response.ErrorWithMessage("更新模板失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteTemplate 删除任务模板
// @Tags 定时任务管理
// @Summary 删除任务模板
// @Description 删除指定的任务模板
// @Accept json
// @Produce json
// @Param id path uint64 true "模板ID"
// @Success 200 {object} response.Response "删除成功"
// @Router /api/cron/templates/{id} [delete]
func (a *CronApi) DeleteTemplate(c *gin.Context) {
	templateIDStr := c.Param("id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("无效的模板ID", c)
		return
	}

	// 删除模板
	if err := service.ServiceGroupManagerAPP.C2ServiceGroup.CronService.DeleteTemplate(templateID); err != nil {
		global.LOG.Error("删除任务模板失败", zap.Error(err))
		response.ErrorWithMessage("删除模板失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// ValidateCronExpr 验证Cron表达式
// @Tags 定时任务管理
// @Summary 验证Cron表达式
// @Description 验证Cron表达式的有效性并返回描述信息
// @Accept json
// @Produce json
// @Param data body cronreq.ValidateCronExprRequest true "验证请求"
// @Success 200 {object} response.Response{data=cronreq.ValidateCronExprResponse} "验证成功"
// @Router /api/cron/validate [post]
func (a *CronApi) ValidateCronExpr(c *gin.Context) {
	var req cronreq.ValidateCronExprRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 验证Cron表达式
	resp := cronreq.ValidateCronExprResponse{
		Valid: true,
	}

	if err := utils.ValidateCronExpression(req.CronExpr); err != nil {
		resp.Valid = false
		resp.Error = err.Error()
	} else {
		resp.Description = utils.DescribeCronExpression(req.CronExpr)

		// 获取接下来5次执行时间
		if times, err := utils.GetNextRunTimes(req.CronExpr, 5); err == nil {
			for _, t := range times {
				resp.NextRuns = append(resp.NextRuns, t.Format("2006-01-02 15:04:05"))
			}
		}
	}

	response.OkWithData(resp, c)
}
