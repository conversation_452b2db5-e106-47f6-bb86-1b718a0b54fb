package sys

import (
	"server/core/manager/ssemgr"
)

// SSEDataProvider SSE数据提供者实现
type SSEDataProvider struct {
	dashboardService DashboardService
}

// NewSSEDataProvider 创建SSE数据提供者
func NewSSEDataProvider() *SSEDataProvider {
	return &SSEDataProvider{
		dashboardService: DashboardService{},
	}
}

// GetDashboardStats 获取仪表盘统计数据
func (p *SSEDataProvider) GetDashboardStats() (interface{}, error) {
	return p.dashboardService.GetDashboardStats()
}

// GetSystemInfo 获取系统信息
func (p *SSEDataProvider) GetSystemInfo() (interface{}, error) {
	return p.dashboardService.GetSystemInfo()
}

// GetNetworkTopology 获取网络拓扑数据
func (p *SSEDataProvider) GetNetworkTopology() (interface{}, error) {
	return p.dashboardService.GetNetworkTopology()
}

// 确保实现了接口
var _ ssemgr.DataProvider = (*SSEDataProvider)(nil)
