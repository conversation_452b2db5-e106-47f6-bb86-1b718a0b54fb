package c2

import (
	"encoding/json"
	"fmt"
	"regexp"
	"server/global"
	cronreq "server/model/request/cron"
	"server/model/sys"
	"server/model/task"
	"server/utils"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

type CronService struct{}

// CreateTask 创建定时任务
func (s *CronService) CreateTask(req cronreq.CreateCronTaskRequest, userID uint) (*task.CronTask, error) {
	// 验证客户端是否存在
	var client sys.Client
	if err := global.DB.First(&client, req.ClientID).Error; err != nil {
		return nil, fmt.Errorf("客户端不存在")
	}

	// 验证Cron表达式
	if err := utils.ValidateCronExpression(req.CronExpr); err != nil {
		return nil, fmt.Errorf("Cron表达式无效: %w", err)
	}

	// 序列化参数
	paramsJSON := ""
	if req.Params != nil {
		paramsBytes, err := json.Marshal(req.Params)
		if err != nil {
			return nil, fmt.Errorf("参数序列化失败: %w", err)
		}
		paramsJSON = string(paramsBytes)
	}

	// 创建任务
	cronTask := &task.CronTask{
		Name:        req.Name,
		Description: req.Description,
		ClientID:    req.ClientID,
		TaskType:    req.TaskType,
		CronExpr:    req.CronExpr,
		Command:     req.Command,
		Params:      paramsJSON,
		Status:      task.TaskStatusStopped, // 默认停止状态
		Timeout:     req.Timeout,
		RetryCount:  req.RetryCount,
		CreatedBy:   userID,
	}

	// 保存到数据库
	if err := global.DB.Create(cronTask).Error; err != nil {
		return nil, fmt.Errorf("创建任务失败: %w", err)
	}

	global.LOG.Info("创建定时任务成功",
		zap.Uint64("taskID", cronTask.ID),
		zap.String("name", cronTask.Name),
		zap.Uint("userID", userID))

	return cronTask, nil
}

// UpdateTask 更新定时任务
func (s *CronService) UpdateTask(taskID uint64, req cronreq.UpdateCronTaskRequest) error {
	// 查找任务
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return fmt.Errorf("任务不存在")
	}

	// 如果任务正在运行，需要先停止
	if cronTask.Status == task.TaskStatusActive {
		if err := s.StopTask(taskID); err != nil {
			return fmt.Errorf("停止任务失败: %w", err)
		}
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.CronExpr != "" {
		// 验证Cron表达式
		if err := utils.ValidateCronExpression(req.CronExpr); err != nil {
			return fmt.Errorf("Cron表达式无效: %w", err)
		}
		updates["cron_expr"] = req.CronExpr
	}
	if req.Command != "" {
		updates["command"] = req.Command
	}
	if req.Params != nil {
		paramsBytes, err := json.Marshal(req.Params)
		if err != nil {
			return fmt.Errorf("参数序列化失败: %w", err)
		}
		updates["params"] = string(paramsBytes)
	}
	if req.Timeout > 0 {
		updates["timeout"] = req.Timeout
	}
	if req.RetryCount >= 0 {
		updates["retry_count"] = req.RetryCount
	}

	// 更新数据库
	if err := global.DB.Model(&cronTask).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新任务失败: %w", err)
	}

	// 如果是定时截图任务，需要同步更新原始的TimedScreenshotTask
	if cronTask.SourceType == "screenshot_timer" && cronTask.SourceID != nil {
		if err := s.syncScreenshotTaskUpdate(cronTask, updates); err != nil {
			global.LOG.Warn("同步定时截图任务失败", zap.Error(err))
			// 不返回错误，因为定时任务已经更新成功
		}
	}

	global.LOG.Info("更新定时任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// DeleteTask 删除定时任务
func (s *CronService) DeleteTask(taskID uint64) error {
	// 查找任务
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return fmt.Errorf("任务不存在")
	}

	// 检查是否是由其他系统管理的任务
	if cronTask.SourceType == "screenshot_timer" && cronTask.SourceID != nil {
		// 这是定时截图任务，需要停止原始的定时截图
		// 直接更新TimedScreenshotTask状态为stopped
		if err := global.DB.Model(&task.TimedScreenshotTask{}).
			Where("id = ?", *cronTask.SourceID).
			Update("status", "stopped").Error; err != nil {
			global.LOG.Warn("停止定时截图任务失败", zap.Error(err))
		} else {
			global.LOG.Info("已停止对应的定时截图任务",
				zap.Uint64("cronTaskID", taskID),
				zap.Uint64("timedTaskID", *cronTask.SourceID),
				zap.Uint("clientID", cronTask.ClientID))
		}
	} else {
		// 普通定时任务，如果正在运行，先停止
		if cronTask.Status == task.TaskStatusActive {
			if err := s.StopTask(taskID); err != nil {
				global.LOG.Warn("停止任务失败", zap.Error(err))
			}
		}
	}

	// 删除任务
	if err := global.DB.Delete(&cronTask).Error; err != nil {
		return fmt.Errorf("删除任务失败: %w", err)
	}

	global.LOG.Info("删除定时任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// GetTask 获取任务详情
func (s *CronService) GetTask(taskID uint64) (*task.CronTask, error) {
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return nil, fmt.Errorf("任务不存在")
	}
	return &cronTask, nil
}

// GetTaskList 获取任务列表
func (s *CronService) GetTaskList(req cronreq.CronTaskListRequest) (*cronreq.CronTaskListResponse, error) {
	var tasks []task.CronTask
	var total int64

	// 构建查询
	query := global.DB.Model(&task.CronTask{})

	// 添加筛选条件
	if req.ClientID > 0 {
		query = query.Where("client_id = ?", req.ClientID)
	}
	if req.TaskType != "" {
		query = query.Where("task_type = ?", req.TaskType)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取任务总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	// 转换为响应格式
	var taskResponses []cronreq.CronTaskResponse
	for _, cronTask := range tasks {
		taskResponse := s.convertToTaskResponse(&cronTask)
		taskResponses = append(taskResponses, taskResponse)
	}

	return &cronreq.CronTaskListResponse{
		List:     taskResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// StartTask 启动任务
func (s *CronService) StartTask(taskID uint64) error {
	// 查找任务
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return fmt.Errorf("任务不存在")
	}

	// 检查任务状态
	if cronTask.Status == task.TaskStatusActive {
		return fmt.Errorf("任务已在运行中")
	}

	// 更新任务状态
	cronTask.Status = task.TaskStatusActive
	if err := global.DB.Save(&cronTask).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	// 添加到Cron管理器
	if global.CronManager != nil {
		// 创建任务副本，确保cron表达式格式正确
		tempTask := cronTask
		tempTask.CronExpr = s.ensureSixFieldCronExpr(cronTask.CronExpr)

		if err := global.CronManager.AddCronTask(&tempTask); err != nil {
			global.LOG.Warn("添加任务到Cron管理器失败", zap.Error(err))
			// 不返回错误，因为数据库状态已经更新
		}
	}

	global.LOG.Info("启动定时任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// StopTask 停止任务
func (s *CronService) StopTask(taskID uint64) error {
	// 查找任务
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return fmt.Errorf("任务不存在")
	}

	// 检查任务状态
	if cronTask.Status == task.TaskStatusStopped {
		return fmt.Errorf("任务已停止")
	}

	// 从Cron管理器中移除
	if global.CronManager != nil {
		if err := global.CronManager.RemoveCronTask(taskID); err != nil {
			global.LOG.Warn("从Cron管理器移除任务失败", zap.Error(err))
			// 不返回错误，继续更新数据库状态
		}
	}

	// 更新任务状态
	cronTask.Status = task.TaskStatusStopped
	if err := global.DB.Save(&cronTask).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	global.LOG.Info("停止定时任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// PauseTask 暂停任务
func (s *CronService) PauseTask(taskID uint64) error {
	// 查找任务
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return fmt.Errorf("任务不存在")
	}

	// 检查任务状态
	if cronTask.Status != task.TaskStatusActive {
		return fmt.Errorf("只能暂停运行中的任务")
	}

	// TODO: 从Cron管理器中移除
	// 暂时先更新数据库状态

	// 更新任务状态
	cronTask.Status = task.TaskStatusPaused
	if err := global.DB.Save(&cronTask).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	global.LOG.Info("暂停定时任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// ExecuteTask 立即执行任务
func (s *CronService) ExecuteTask(taskID uint64) error {
	// 查找任务
	var cronTask task.CronTask
	if err := global.DB.First(&cronTask, taskID).Error; err != nil {
		return fmt.Errorf("任务不存在")
	}

	// 如果任务正在运行，通过Cron管理器立即执行
	if cronTask.Status == task.TaskStatusActive && global.CronManager != nil {
		if err := global.CronManager.ExecuteTaskNow(taskID); err != nil {
			return fmt.Errorf("立即执行任务失败: %w", err)
		}
	} else {
		// 对于已停止的任务，直接执行一次
		if global.CronManager != nil {
			// 创建临时任务副本，确保cron表达式格式正确
			tempTask := cronTask
			tempTask.CronExpr = s.ensureSixFieldCronExpr(cronTask.CronExpr)

			// 临时添加任务到CronManager并立即执行
			if err := global.CronManager.AddCronTask(&tempTask); err != nil {
				return fmt.Errorf("临时添加任务失败: %w", err)
			}

			// 立即执行
			if err := global.CronManager.ExecuteTaskNow(taskID); err != nil {
				// 执行失败，移除临时添加的任务
				global.CronManager.RemoveCronTask(taskID)
				return fmt.Errorf("立即执行任务失败: %w", err)
			}

			// 执行成功，移除临时添加的任务（因为原任务是停止状态）
			global.CronManager.RemoveCronTask(taskID)
		} else {
			return fmt.Errorf("Cron管理器未初始化")
		}
	}

	global.LOG.Info("立即执行定时任务", zap.Uint64("taskID", taskID))
	return nil
}

// GetExecutionList 获取执行记录列表
func (s *CronService) GetExecutionList(req cronreq.CronExecutionListRequest) (*cronreq.CronExecutionListResponse, error) {
	var executions []task.CronExecution
	var total int64

	// 构建查询条件
	query := global.DB.Model(&task.CronExecution{})

	// 任务ID筛选
	if req.TaskID > 0 {
		query = query.Where("task_id = ?", req.TaskID)
	}

	// 客户端ID筛选
	if req.ClientID > 0 {
		query = query.Where("client_id = ?", req.ClientID)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取执行记录总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&executions).Error; err != nil {
		return nil, fmt.Errorf("获取执行记录列表失败: %w", err)
	}

	// 转换为响应格式
	var executionResponses []cronreq.CronExecutionResponse
	for _, execution := range executions {
		// 获取任务名称
		var cronTask task.CronTask
		taskName := "未知任务"
		if err := global.DB.Select("name").First(&cronTask, execution.TaskID).Error; err == nil {
			taskName = cronTask.Name
		}

		// 获取客户端名称
		var client sys.Client
		clientName := "未知客户端"
		if err := global.DB.Select("hostname").First(&client, execution.ClientID).Error; err == nil {
			clientName = client.Hostname
		}

		// 状态显示名称
		statusDisplay := getStatusDisplay(execution.Status)

		executionResponse := cronreq.CronExecutionResponse{
			ID:            execution.ID,
			TaskID:        execution.TaskID,
			TaskName:      taskName,
			ClientID:      execution.ClientID,
			ClientName:    clientName,
			Status:        execution.Status,
			StatusDisplay: statusDisplay,
			Output:        execution.Output,
			ErrorMsg:      execution.ErrorMsg,
			StartTime:     execution.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:       execution.EndTime.Format("2006-01-02 15:04:05"),
			Duration:      execution.Duration,
			CreatedAt:     execution.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		executionResponses = append(executionResponses, executionResponse)
	}

	return &cronreq.CronExecutionListResponse{
		List:     executionResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetExecution 获取执行记录详情
func (s *CronService) GetExecution(executionID uint64) (*cronreq.CronExecutionResponse, error) {
	var execution task.CronExecution
	if err := global.DB.First(&execution, executionID).Error; err != nil {
		return nil, fmt.Errorf("执行记录不存在")
	}

	// 获取任务名称
	var cronTask task.CronTask
	taskName := "未知任务"
	if err := global.DB.Select("name").First(&cronTask, execution.TaskID).Error; err == nil {
		taskName = cronTask.Name
	}

	// 获取客户端名称
	var client sys.Client
	clientName := "未知客户端"
	if err := global.DB.Select("hostname").First(&client, execution.ClientID).Error; err == nil {
		clientName = client.Hostname
	}

	// 状态显示名称
	statusDisplay := getStatusDisplay(execution.Status)

	return &cronreq.CronExecutionResponse{
		ID:            execution.ID,
		TaskID:        execution.TaskID,
		TaskName:      taskName,
		ClientID:      execution.ClientID,
		ClientName:    clientName,
		Status:        execution.Status,
		StatusDisplay: statusDisplay,
		Output:        execution.Output,
		ErrorMsg:      execution.ErrorMsg,
		StartTime:     execution.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:       execution.EndTime.Format("2006-01-02 15:04:05"),
		Duration:      execution.Duration,
		CreatedAt:     execution.CreatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// getStatusDisplay 获取状态显示名称
func getStatusDisplay(status string) string {
	switch status {
	case "success":
		return "成功"
	case "failed":
		return "失败"
	case "timeout":
		return "超时"
	case "cancelled":
		return "已取消"
	default:
		return "未知"
	}
}

// GetTemplateList 获取任务模板列表
func (s *CronService) GetTemplateList() (*cronreq.CronTemplateListResponse, error) {
	var templates []task.CronTemplate

	// 查询所有模板，按创建时间倒序
	if err := global.DB.Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("获取模板列表失败: %w", err)
	}

	// 转换为响应格式
	var templateResponses []cronreq.CronTemplateResponse
	for _, template := range templates {
		// 获取创建者名称
		var user sys.SysUser
		createdByName := "未知用户"
		if template.CreatedBy > 0 {
			if err := global.DB.Select("username").First(&user, template.CreatedBy).Error; err == nil {
				createdByName = user.Username
			}
		}

		// 分类和任务类型显示名称
		categoryDisplay := getCategoryDisplay(template.Category)
		taskTypeDisplay := getTaskTypeDisplay(template.TaskType)

		templateResponse := cronreq.CronTemplateResponse{
			ID:              template.ID,
			Name:            template.Name,
			Description:     template.Description,
			Category:        template.Category,
			CategoryDisplay: categoryDisplay,
			TaskType:        template.TaskType,
			TaskTypeDisplay: taskTypeDisplay,
			CronExpr:        template.CronExpr,
			Command:         template.Command,
			Params:          template.Params,
			Timeout:         template.Timeout,
			RetryCount:      template.RetryCount,
			IsBuiltIn:       template.IsBuiltIn,
			CreatedBy:       template.CreatedBy,
			CreatedByName:   createdByName,
			CreatedAt:       template.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:       template.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		templateResponses = append(templateResponses, templateResponse)
	}

	return &cronreq.CronTemplateListResponse{
		List: templateResponses,
	}, nil
}

// CreateTemplate 创建任务模板
func (s *CronService) CreateTemplate(req cronreq.CronTemplateRequest, userID uint) (*task.CronTemplate, error) {
	// 验证Cron表达式
	if err := utils.ValidateCronExpression(req.CronExpr); err != nil {
		return nil, fmt.Errorf("Cron表达式无效: %w", err)
	}

	// 序列化参数
	paramsJSON := ""
	if req.Params != nil {
		paramsBytes, err := json.Marshal(req.Params)
		if err != nil {
			return nil, fmt.Errorf("参数序列化失败: %w", err)
		}
		paramsJSON = string(paramsBytes)
	}

	// 创建模板
	template := &task.CronTemplate{
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		TaskType:    req.TaskType,
		CronExpr:    req.CronExpr,
		Command:     req.Command,
		Params:      paramsJSON,
		Timeout:     req.Timeout,
		RetryCount:  req.RetryCount,
		IsBuiltIn:   false, // 用户创建的模板不是内置模板
		CreatedBy:   userID,
	}

	// 保存到数据库
	if err := global.DB.Create(template).Error; err != nil {
		return nil, fmt.Errorf("创建模板失败: %w", err)
	}

	global.LOG.Info("创建任务模板成功",
		zap.Uint64("templateID", template.ID),
		zap.String("name", template.Name),
		zap.Uint("userID", userID))

	return template, nil
}

// GetTemplate 获取任务模板详情
func (s *CronService) GetTemplate(templateID uint64) (*task.CronTemplate, error) {
	var template task.CronTemplate
	if err := global.DB.First(&template, templateID).Error; err != nil {
		return nil, fmt.Errorf("模板不存在")
	}
	return &template, nil
}

// UpdateTemplate 更新任务模板
func (s *CronService) UpdateTemplate(templateID uint64, req cronreq.CronTemplateRequest, userID uint) error {
	// 查找模板
	var template task.CronTemplate
	if err := global.DB.First(&template, templateID).Error; err != nil {
		return fmt.Errorf("模板不存在")
	}

	// 检查是否为内置模板
	if template.IsBuiltIn {
		return fmt.Errorf("内置模板不能修改")
	}

	// 验证Cron表达式
	if err := utils.ValidateCronExpression(req.CronExpr); err != nil {
		return fmt.Errorf("Cron表达式无效: %w", err)
	}

	// 序列化参数
	paramsJSON := ""
	if req.Params != nil {
		paramsBytes, err := json.Marshal(req.Params)
		if err != nil {
			return fmt.Errorf("参数序列化失败: %w", err)
		}
		paramsJSON = string(paramsBytes)
	}

	// 更新模板字段
	template.Name = req.Name
	template.Description = req.Description
	template.Category = req.Category
	template.TaskType = req.TaskType
	template.CronExpr = req.CronExpr
	template.Command = req.Command
	template.Params = paramsJSON
	template.Timeout = req.Timeout
	template.RetryCount = req.RetryCount

	// 保存到数据库
	if err := global.DB.Save(&template).Error; err != nil {
		return fmt.Errorf("更新模板失败: %w", err)
	}

	global.LOG.Info("更新任务模板成功",
		zap.Uint64("templateID", templateID),
		zap.String("name", template.Name),
		zap.Uint("userID", userID))

	return nil
}

// DeleteTemplate 删除任务模板
func (s *CronService) DeleteTemplate(templateID uint64) error {
	// 查找模板
	var template task.CronTemplate
	if err := global.DB.First(&template, templateID).Error; err != nil {
		return fmt.Errorf("模板不存在")
	}

	// 检查是否为内置模板
	if template.IsBuiltIn {
		return fmt.Errorf("内置模板不能删除")
	}

	// 删除模板
	if err := global.DB.Delete(&template).Error; err != nil {
		return fmt.Errorf("删除模板失败: %w", err)
	}

	global.LOG.Info("删除任务模板成功", zap.Uint64("templateID", templateID))
	return nil
}

// getCategoryDisplay 获取分类显示名称
func getCategoryDisplay(category string) string {
	switch category {
	case task.TemplateCategorySystem:
		return "系统管理"
	case task.TemplateCategoryMonitor:
		return "监控类"
	case task.TemplateCategoryMaintain:
		return "维护类"
	case task.TemplateCategoryBackup:
		return "备份类"
	case task.TemplateCategorySecurity:
		return "安全类"
	case task.TemplateCategoryCustom:
		return "自定义"
	default:
		return "未知分类"
	}
}

// getTaskTypeDisplay 获取任务类型显示名称
func getTaskTypeDisplay(taskType string) string {
	switch taskType {
	case task.TaskTypeCommand:
		return "命令执行"
	case task.TaskTypeScreenshot:
		return "截图任务"
	case task.TaskTypeScript:
		return "脚本执行"
	default:
		return "未知类型"
	}
}

// convertToTaskResponse 转换为任务响应格式
func (s *CronService) convertToTaskResponse(cronTask *task.CronTask) cronreq.CronTaskResponse {
	// 获取客户端名称
	var client sys.Client
	clientName := "未知客户端"
	if err := global.DB.First(&client, cronTask.ClientID).Error; err == nil {
		if client.Hostname != "" {
			clientName = client.Hostname
		} else if client.RemoteAddr != "" {
			clientName = client.RemoteAddr
		}
	}

	// 获取创建者名称
	var user sys.SysUser
	createdByName := "未知用户"
	if err := global.DB.First(&user, cronTask.CreatedBy).Error; err == nil {
		createdByName = user.Username
	}

	// 格式化时间
	createdAt := cronTask.CreatedAt.Format("2006-01-02 15:04:05")
	updatedAt := cronTask.UpdatedAt.Format("2006-01-02 15:04:05")
	nextRunAt := ""
	lastRunAt := ""

	if cronTask.NextRunAt != nil {
		nextRunAt = cronTask.NextRunAt.Format("2006-01-02 15:04:05")
	}
	if cronTask.LastRunAt != nil {
		lastRunAt = cronTask.LastRunAt.Format("2006-01-02 15:04:05")
	}

	// 判断是否由其他系统管理
	isManaged := cronTask.SourceType != "" && cronTask.SourceType != "manual"

	return cronreq.CronTaskResponse{
		ID:              cronTask.ID,
		Name:            cronTask.Name,
		Description:     cronTask.Description,
		ClientID:        cronTask.ClientID,
		ClientName:      clientName,
		TaskType:        cronTask.TaskType,
		TaskTypeDisplay: task.GetTaskTypeDisplayName(cronTask.TaskType),
		CronExpr:        cronTask.CronExpr,
		Command:         cronTask.Command,
		Params:          cronTask.Params,
		Status:          cronTask.Status,
		StatusDisplay:   task.GetTaskStatusDisplayName(cronTask.Status),
		Timeout:         cronTask.Timeout,
		RetryCount:      cronTask.RetryCount,
		CreatedBy:       cronTask.CreatedBy,
		CreatedByName:   createdByName,
		SourceType:      cronTask.SourceType,
		SourceID:        cronTask.SourceID,
		IsManaged:       isManaged,
		CreatedAt:       createdAt,
		UpdatedAt:       updatedAt,
		NextRunAt:       nextRunAt,
		LastRunAt:       lastRunAt,
	}
}

// syncScreenshotTaskUpdate 同步更新定时截图任务
func (s *CronService) syncScreenshotTaskUpdate(cronTask task.CronTask, updates map[string]interface{}) error {
	// 构建TimedScreenshotTask的更新字段
	screenshotUpdates := make(map[string]interface{})

	// 映射可同步的字段
	if name, exists := updates["name"]; exists {
		// 从定时截图任务名称中提取客户端ID部分，保持格式一致
		screenshotUpdates["name"] = name
	}

	if description, exists := updates["description"]; exists {
		screenshotUpdates["description"] = description
	}

	if cronExpr, exists := updates["cron_expr"]; exists {
		// 从cron表达式反推间隔秒数（仅支持简单的秒级间隔）
		if intervalSeconds := s.cronExprToSeconds(cronExpr.(string)); intervalSeconds > 0 {
			screenshotUpdates["interval_seconds"] = intervalSeconds
		}
	}

	// 如果有需要更新的字段，执行更新
	if len(screenshotUpdates) > 0 {
		if err := global.DB.Model(&task.TimedScreenshotTask{}).
			Where("id = ?", *cronTask.SourceID).
			Updates(screenshotUpdates).Error; err != nil {
			return fmt.Errorf("更新TimedScreenshotTask失败: %w", err)
		}

		global.LOG.Info("已同步更新定时截图任务",
			zap.Uint64("cronTaskID", cronTask.ID),
			zap.Uint64("timedTaskID", *cronTask.SourceID),
			zap.Any("updates", screenshotUpdates))
	}

	return nil
}

// cronExprToSeconds 从cron表达式提取间隔秒数（仅支持简单的秒级间隔）
func (s *CronService) cronExprToSeconds(cronExpr string) int {
	// 简单解析 "*/N * * * * *" 格式的表达式
	// 这里只处理最常见的秒级间隔格式
	if len(cronExpr) >= 3 && cronExpr[:2] == "*/" {
		// 查找第一个空格
		spaceIndex := -1
		for i, char := range cronExpr {
			if char == ' ' {
				spaceIndex = i
				break
			}
		}

		if spaceIndex > 2 {
			secondsStr := cronExpr[2:spaceIndex]
			if seconds, err := strconv.Atoi(secondsStr); err == nil && seconds > 0 {
				return seconds
			}
		}
	}

	return 0 // 无法解析或不支持的格式
}

// ensureSixFieldCronExpr 确保cron表达式是6字段格式（秒 分 时 日 月 周）
func (s *CronService) ensureSixFieldCronExpr(cronExpr string) string {
	// 清理输入：去除前后空格，压缩多个空格为单个空格
	cronExpr = strings.TrimSpace(cronExpr)
	if cronExpr == "" {
		return "0 0 0 * * *" // 默认：每天午夜执行
	}

	// 使用正则表达式分割，处理多个空格的情况
	re := regexp.MustCompile(`\s+`)
	cronExpr = re.ReplaceAllString(cronExpr, " ")
	fields := strings.Fields(cronExpr)

	switch len(fields) {
	case 6:
		// 已经是6字段格式，验证每个字段的合法性
		return s.validateAndNormalizeSixFields(fields)
	case 5:
		// 5字段格式，在前面添加秒字段
		return s.validateAndNormalizeSixFields(append([]string{"0"}, fields...))
	case 4:
		// 4字段格式（分 时 日 月），添加秒和周字段
		return s.validateAndNormalizeSixFields(append([]string{"0"}, append(fields, "*")...))
	case 3:
		// 3字段格式（时 日 月），添加秒、分和周字段
		return s.validateAndNormalizeSixFields([]string{"0", "0", fields[0], fields[1], fields[2], "*"})
	default:
		// 其他情况，返回默认表达式
		global.LOG.Warn("无效的cron表达式字段数量", zap.String("cronExpr", cronExpr), zap.Int("fields", len(fields)))
		return "0 0 0 * * *" // 默认：每天午夜执行
	}
}

// validateAndNormalizeSixFields 验证并规范化6字段cron表达式
func (s *CronService) validateAndNormalizeSixFields(fields []string) string {
	if len(fields) != 6 {
		return "0 0 0 * * *"
	}

	// 验证每个字段的基本格式
	for i, field := range fields {
		if field == "" {
			fields[i] = "*"
		}
		// 基本的字符验证：只允许数字、*、/、-、,
		if !regexp.MustCompile(`^[0-9*,/\-]+$`).MatchString(field) {
			global.LOG.Warn("cron字段包含非法字符", zap.String("field", field), zap.Int("position", i))
			fields[i] = "*"
		}
	}

	// 特殊处理：确保周字段的合法性（0-7，其中0和7都表示周日）
	weekField := fields[5]
	if weekField != "*" && !regexp.MustCompile(`^[0-7,/\-]+$`).MatchString(weekField) {
		fields[5] = "*"
	}

	return strings.Join(fields, " ")
}
