<template>
  <div class="cron-create">
    <div class="page-header">
      <h2>创建定时任务</h2>
      <a-button @click="goBack">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回列表
      </a-button>
    </div>

    <a-card>
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
        @finish="handleSubmit"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="任务名称" name="name">
              <a-input v-model:value="form.name" placeholder="请输入任务名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="目标客户端" name="client_id">
              <a-select
                v-model:value="form.client_id"
                placeholder="选择目标客户端"
                show-search
                :filter-option="filterClient"
              >
                <a-select-option
                  v-for="client in clientList"
                  :key="client.id"
                  :value="client.id"
                >
                  {{ client.hostname || client.remoteAddr }} ({{ client.os }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="任务类型" name="task_type">
              <a-select
                v-model:value="form.task_type"
                placeholder="选择任务类型"
                @change="handleTaskTypeChange"
              >
                <a-select-option value="command">命令执行</a-select-option>
                <a-select-option value="screenshot">截图任务</a-select-option>
                <a-select-option value="script">脚本执行</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="任务描述" name="description">
              <a-textarea
                v-model:value="form.description"
                placeholder="请输入任务描述（可选）"
                :rows="3"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Cron表达式" name="cron_expr">
              <CronBuilder
                v-model="form.cron_expr"
                @validate="handleCronValidation"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 脚本类型选择 -->
        <a-form-item
          v-if="form.task_type === 'script'"
          label="脚本类型"
          name="script_type"
        >
          <a-select
            v-model:value="form.script_type"
            placeholder="选择脚本类型"
            @change="handleScriptTypeChange"
          >
            <a-select-option
              v-for="scriptType in availableScriptTypes"
              :key="scriptType.value"
              :value="scriptType.value"
            >
              {{ scriptType.label }}
            </a-select-option>
          </a-select>
          <div class="script-type-help" style="margin-top: 8px;">
            <a-alert
              :message="getScriptTypeDescription(form.script_type)"
              type="info"
              show-icon
            />
          </div>
        </a-form-item>

        <!-- 命令/脚本内容 -->
        <a-form-item
          v-if="form.task_type === 'command' || form.task_type === 'script'"
          :label="form.task_type === 'command' ? '执行命令' : '脚本内容'"
          name="command"
        >
          <a-textarea
            v-model:value="form.command"
            :placeholder="getCommandPlaceholder()"
            :rows="6"
            :auto-size="{ minRows: 6, maxRows: 12 }"
            class="command-textarea"
          />
          <div v-if="form.task_type === 'command'" class="command-help">
            <a-alert
              message="命令执行说明"
              description="命令将在客户端使用exec.Command执行，支持大部分系统命令。执行结果会记录在任务日志中。"
              type="info"
              show-icon
              style="margin-top: 8px"
            />
          </div>
          <div v-if="form.task_type === 'script'" class="script-help">
            <a-alert
              message="脚本执行说明"
              :description="getScriptExecutionDescription()"
              type="info"
              show-icon
              style="margin-top: 8px"
            />
          </div>
        </a-form-item>

        <!-- 截图任务参数 -->
        <div v-if="form.task_type === 'screenshot'">
          <a-form-item label="截图参数">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="截图类型">
                  <a-select v-model:value="screenshotParams.type">
                    <a-select-option :value="0">全屏截图</a-select-option>
                    <a-select-option :value="1">活动窗口</a-select-option>
                    <a-select-option :value="2">指定区域</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="图片质量">
                  <a-slider
                    v-model:value="screenshotParams.quality"
                    :min="1"
                    :max="100"
                    :marks="{ 1: '1%', 50: '50%', 100: '100%' }"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="图片格式">
                  <a-select v-model:value="screenshotParams.format">
                    <a-select-option value="png">PNG</a-select-option>
                    <a-select-option value="jpg">JPG</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>
        </div>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="超时时间(秒)" name="timeout">
              <a-input-number
                v-model:value="form.timeout"
                :min="1"
                :max="3600"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="重试次数" name="retry_count">
              <a-input-number
                v-model:value="form.retry_count"
                :min="0"
                :max="10"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              创建任务
            </a-button>
            <a-button @click="goBack">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import * as clientApi from '@/api/client'
import * as cronApi from '@/api/cron'
import CronBuilder from './components/CronBuilder.vue'

const router = useRouter()
const loading = ref(false)
const formRef = ref()
const clientList = ref([])

// 表单数据
const form = reactive({
  name: '',
  description: '',
  client_id: undefined,
  task_type: 'command',
  script_type: 'sh', // 默认脚本类型
  cron_expr: '',
  command: '',
  timeout: 300,
  retry_count: 0,
  params: {}
})

// 截图参数
const screenshotParams = reactive({
  type: 0,
  quality: 80,
  format: 'png',
  monitor_index: 0
})

// 脚本类型定义
const scriptTypes = {
  windows: [
    { value: 'bat', label: 'Batch (.bat)', description: 'Windows批处理脚本' },
    { value: 'ps1', label: 'PowerShell (.ps1)', description: 'PowerShell脚本' },
    { value: 'cmd', label: 'Command (.cmd)', description: 'Windows命令脚本' },
    { value: 'py', label: 'Python (.py)', description: 'Python脚本' }
  ],
  linux: [
    { value: 'sh', label: 'Shell (.sh)', description: 'Shell脚本' },
    { value: 'py', label: 'Python (.py)', description: 'Python脚本' },
    { value: 'pl', label: 'Perl (.pl)', description: 'Perl脚本' }
  ],
  darwin: [
    { value: 'sh', label: 'Shell (.sh)', description: 'Shell脚本' },
    { value: 'py', label: 'Python (.py)', description: 'Python脚本' },
    { value: 'pl', label: 'Perl (.pl)', description: 'Perl脚本' }
  ]
}

// 计算当前客户端可用的脚本类型
const availableScriptTypes = computed(() => {
  if (!form.client_id) return []

  const client = clientList.value.find(c => c.id === form.client_id)
  if (!client) return []

  const os = client.os.toLowerCase()
  if (os.includes('windows')) {
    return scriptTypes.windows
  } else if (os.includes('linux')) {
    return scriptTypes.linux
  } else if (os.includes('darwin') || os.includes('macos')) {
    return scriptTypes.darwin
  }

  // 默认返回Linux脚本类型
  return scriptTypes.linux
})

// Cron表达式验证结果
const cronValidation = reactive({
  valid: null,
  error: '',
  description: '',
  next_runs: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  client_id: [
    { required: true, message: '请选择目标客户端', trigger: 'change' }
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  script_type: [
    {
      required: true,
      message: '请选择脚本类型',
      trigger: 'change',
      validator: (rule, value) => {
        if (form.task_type === 'script' && !value) {
          return Promise.reject('请选择脚本类型')
        }
        return Promise.resolve()
      }
    }
  ],
  cron_expr: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' }
  ],
  command: [
    { required: true, message: '请输入命令或脚本内容', trigger: 'blur' }
  ]
}

// 获取客户端列表
const fetchClientList = async () => {
  try {
    const response = await clientApi.getClientList({ page: 1, page_size: 1000 })
    if (response.code === 200) {
      clientList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取客户端列表失败:', error)
  }
}

// 处理任务类型变化
const handleTaskTypeChange = (type) => {
  form.command = ''
  if (type === 'screenshot') {
    form.params = { ...screenshotParams }
  } else if (type === 'script') {
    // 设置默认脚本类型
    const defaultScriptType = availableScriptTypes.value[0]?.value || 'sh'
    form.script_type = defaultScriptType
    form.params = { script_type: defaultScriptType }
  } else {
    form.params = {}
  }
}

// 处理脚本类型变化
const handleScriptTypeChange = (scriptType) => {
  form.params = { script_type: scriptType }
}

// 获取脚本类型描述
const getScriptTypeDescription = (scriptType) => {
  const allTypes = [...scriptTypes.windows, ...scriptTypes.linux, ...scriptTypes.darwin]
  const type = allTypes.find(t => t.value === scriptType)
  return type ? type.description : '请选择脚本类型'
}

// 获取命令输入框占位符
const getCommandPlaceholder = () => {
  if (form.task_type === 'command') {
    return '请输入要执行的命令，例如：ls -la 或 echo Hello World'
  } else if (form.task_type === 'script') {
    const scriptType = form.script_type
    switch (scriptType) {
      case 'bat':
      case 'cmd':
        return '@echo off\necho Hello World\npause'
      case 'ps1':
        return 'Write-Host "Hello World"\nGet-Date'
      case 'sh':
        return '#!/bin/bash\necho "Hello World"\ndate'
      case 'py':
        return 'import datetime\nprint("Hello World")\nprint(datetime.datetime.now())'
      case 'pl':
        return '#!/usr/bin/perl\nprint "Hello World\\n";\nprint scalar localtime, "\\n";'
      default:
        return '请输入脚本内容'
    }
  }
  return ''
}

// 获取脚本执行说明
const getScriptExecutionDescription = () => {
  const scriptType = form.script_type
  switch (scriptType) {
    case 'bat':
    case 'cmd':
      return '脚本将在Windows客户端使用cmd执行，支持批处理命令和变量。'
    case 'ps1':
      return '脚本将在Windows客户端使用PowerShell执行，支持PowerShell命令和模块。'
    case 'sh':
      return '脚本将在客户端使用bash执行，支持Shell命令和语法。'
    case 'py':
      return '脚本将在客户端使用Python解释器执行，支持Python语法和库。'
    case 'pl':
      return '脚本将在客户端使用Perl解释器执行，支持Perl语法和模块。'
    default:
      return '脚本将在客户端执行，请确保脚本语法正确。'
  }
}

// 处理Cron表达式验证结果
const handleCronValidation = (result) => {
  Object.assign(cronValidation, result)
}

// 客户端筛选
const filterClient = (input, option) => {
  const client = clientList.value.find(c => c.id === option.value)
  if (!client) return false

  const searchText = input.toLowerCase()
  return (
    (client.hostname && client.hostname.toLowerCase().includes(searchText)) ||
    (client.remoteAddr && client.remoteAddr.toLowerCase().includes(searchText)) ||
    (client.os && client.os.toLowerCase().includes(searchText))
  )
}

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true

    // 准备提交数据
    const submitData = { ...form }

    // 如果是截图任务，设置参数
    if (form.task_type === 'screenshot') {
      submitData.params = { ...screenshotParams }
    } else if (form.task_type === 'script') {
      // 如果是脚本任务，设置脚本类型参数
      submitData.params = { script_type: form.script_type }
    }

    // 调用API
    await cronApi.createTask(submitData)
    message.success('任务创建成功')
    router.push('/cron/list')
  } catch (error) {
    console.error('创建任务失败:', error)
    message.error('创建任务失败')
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.push('/cron/list')
}

// 从模板加载数据
const loadFromTemplate = async (templateId) => {
  try {
    const response = await cronApi.getTemplate(templateId)
    if (response.code === 200) {
      const template = response.data
      // 填充表单数据
      form.name = template.name + ' (副本)'
      form.description = template.description
      form.task_type = template.task_type
      form.cron_expr = template.cron_expr
      form.command = template.command
      form.timeout = template.timeout
      form.retry_count = template.retry_count

      message.success('模板加载成功')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    message.error('加载模板失败')
  }
}

onMounted(() => {
  fetchClientList()

  // 检查是否有模板参数
  const templateId = router.currentRoute.value.query.template_id
  if (templateId) {
    loadFromTemplate(templateId)
  }
})
</script>

<style scoped lang="scss">
.cron-create {
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
    }
  }

  .error-text {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
  }

  .success-text {
    color: #52c41a;
    font-size: 12px;
    margin-top: 4px;
  }

  .command-textarea {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.5;

    :deep(.ant-input) {
      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      line-height: 1.5;
      resize: vertical;
    }
  }
}
</style>
