<template>
  <div class="cron-builder">
    <!-- 模式切换 -->
    <div class="mode-switch">
      <a-radio-group v-model:value="mode" @change="handleModeChange">
        <a-radio-button value="simple">简单模式</a-radio-button>
        <a-radio-button value="advanced">高级模式</a-radio-button>
      </a-radio-group>
    </div>

    <!-- 简单模式 -->
    <div v-if="mode === 'simple'" class="simple-mode">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="执行频率">
            <a-select v-model:value="simpleConfig.type" @change="handleTypeChange">
              <a-select-option value="once">执行一次</a-select-option>
              <a-select-option value="interval">间隔执行</a-select-option>
              <a-select-option value="daily">每天</a-select-option>
              <a-select-option value="weekly">每周</a-select-option>
              <a-select-option value="monthly">每月</a-select-option>
              <a-select-option value="yearly">每年</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 执行一次 -->
      <div v-if="simpleConfig.type === 'once'">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="执行日期">
              <a-date-picker 
                v-model:value="simpleConfig.date" 
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="执行时间">
              <a-time-picker
                v-model:value="simpleConfig.time"
                format="HH:mm:ss"
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 间隔执行 -->
      <div v-if="simpleConfig.type === 'interval'">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="间隔数值">
              <a-input-number 
                v-model:value="simpleConfig.interval.value" 
                :min="1" 
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="间隔单位">
              <a-select v-model:value="simpleConfig.interval.unit" @change="handleSimpleConfigChange">
                <a-select-option value="seconds">秒</a-select-option>
                <a-select-option value="minutes">分钟</a-select-option>
                <a-select-option value="hours">小时</a-select-option>
                <a-select-option value="days">天</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 每天 -->
      <div v-if="simpleConfig.type === 'daily'">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="执行时间">
              <a-time-picker 
                v-model:value="simpleConfig.time" 
                format="HH:mm:ss"
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 每周 -->
      <div v-if="simpleConfig.type === 'weekly'">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="星期">
              <a-select
                v-model:value="simpleConfig.weekdays"
                mode="multiple"
                placeholder="选择星期"
                @change="handleSimpleConfigChange"
              >
                <a-select-option :value="1">星期一</a-select-option>
                <a-select-option :value="2">星期二</a-select-option>
                <a-select-option :value="3">星期三</a-select-option>
                <a-select-option :value="4">星期四</a-select-option>
                <a-select-option :value="5">星期五</a-select-option>
                <a-select-option :value="6">星期六</a-select-option>
                <a-select-option :value="0">星期日</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="执行时间">
              <a-time-picker 
                v-model:value="simpleConfig.time" 
                format="HH:mm:ss"
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 每月 -->
      <div v-if="simpleConfig.type === 'monthly'">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="日期">
              <a-select
                v-model:value="simpleConfig.monthDay"
                placeholder="选择日期"
                @change="handleSimpleConfigChange"
              >
                <a-select-option v-for="day in 31" :key="day" :value="day">
                  {{ day }}日
                </a-select-option>
                <a-select-option value="last">最后一天</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="执行时间">
              <a-time-picker 
                v-model:value="simpleConfig.time" 
                format="HH:mm:ss"
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 每年 -->
      <div v-if="simpleConfig.type === 'yearly'">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="月份">
              <a-select
                v-model:value="simpleConfig.month"
                placeholder="选择月份"
                @change="handleSimpleConfigChange"
              >
                <a-select-option v-for="month in 12" :key="month" :value="month">
                  {{ month }}月
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="日期">
              <a-select 
                v-model:value="simpleConfig.monthDay" 
                placeholder="选择日期"
                @change="handleSimpleConfigChange"
              >
                <a-select-option v-for="day in 31" :key="day" :value="day">
                  {{ day }}日
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="执行时间">
              <a-time-picker
                v-model:value="simpleConfig.time"
                format="HH:mm:ss"
                style="width: 100%"
                @change="handleSimpleConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 高级模式 -->
    <div v-if="mode === 'advanced'" class="advanced-mode">
      <a-form-item label="Cron表达式">
        <a-input 
          v-model:value="cronExpression" 
          placeholder="如: 0 */5 * * * *"
          @change="handleCronChange"
        />
        <div class="cron-help">
          <a-typography-text type="secondary">
            格式: 秒 分 时 日 月 周 (例如: 0 30 8 * * * 表示每天8:30:00执行)
          </a-typography-text>
        </div>
      </a-form-item>
    </div>

    <!-- 预览和说明 -->
    <div class="cron-preview">
      <a-form-item label="表达式预览">
        <a-input :value="cronExpression" readonly />
      </a-form-item>
      <div v-if="cronDescription" class="cron-description">
        <a-typography-text type="success">
          <CheckCircleOutlined /> {{ cronDescription }}
        </a-typography-text>
      </div>
      <div v-if="nextRuns.length > 0" class="next-runs">
        <a-typography-text type="secondary">
          接下来几次执行时间:
        </a-typography-text>
        <ul>
          <li v-for="(time, index) in nextRuns.slice(0, 3)" :key="index">
            {{ time }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { CheckCircleOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import * as cronApi from '@/api/cron'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate'])

// 响应式数据
const mode = ref('simple')
const cronExpression = ref(props.modelValue || '0 0 8 * * *')
const cronDescription = ref('')
const nextRuns = ref([])

// 简单模式配置
const simpleConfig = reactive({
  type: 'daily',
  date: null,
  time: dayjs('08:00:00', 'HH:mm:ss'),
  interval: {
    value: 5,
    unit: 'minutes'
  },
  weekdays: [1, 2, 3, 4, 5], // 工作日
  monthDay: 1,
  month: 1
})

// 监听模式变化
const handleModeChange = () => {
  if (mode.value === 'simple') {
    updateCronExpression()
  }
}

// 监听类型变化
const handleTypeChange = () => {
  if (isModeChanging.value) return

  // 重置相关配置
  if (simpleConfig.type === 'once') {
    simpleConfig.date = dayjs().add(1, 'day')
    simpleConfig.time = dayjs('08:00:00', 'HH:mm:ss')
  } else if (simpleConfig.type === 'daily') {
    simpleConfig.time = dayjs('08:00:00', 'HH:mm:ss')
  } else if (simpleConfig.type === 'weekly') {
    simpleConfig.weekdays = [1, 2, 3, 4, 5]
    simpleConfig.time = dayjs('08:00:00', 'HH:mm:ss')
  }
  updateCronExpression()
}

// 包装的更新函数，用于处理UI事件
const handleSimpleConfigChange = () => {
  if (isModeChanging.value) return
  updateCronExpression()
}

// 更新Cron表达式
const updateCronExpression = () => {
  if (mode.value !== 'simple' || isUpdating.value || isModeChanging.value) return

  isUpdating.value = true
  let cron = ''

  switch (simpleConfig.type) {
    case 'once':
      if (simpleConfig.date && simpleConfig.time) {
        const date = dayjs(simpleConfig.date)
        const time = dayjs(simpleConfig.time)
        cron = `${time.second()} ${time.minute()} ${time.hour()} ${date.date()} ${date.month() + 1} *`
      }
      break

    case 'interval':
      const { value, unit } = simpleConfig.interval
      switch (unit) {
        case 'seconds':
          cron = `*/${value} * * * * *`
          break
        case 'minutes':
          cron = `0 */${value} * * * *`
          break
        case 'hours':
          cron = `0 0 */${value} * * *`
          break
        case 'days':
          cron = `0 0 8 */${value} * *`
          break
      }
      break

    case 'daily':
      if (simpleConfig.time) {
        const time = dayjs(simpleConfig.time)
        cron = `${time.second()} ${time.minute()} ${time.hour()} * * *`
      }
      break

    case 'weekly':
      if (simpleConfig.time && simpleConfig.weekdays.length > 0) {
        const time = dayjs(simpleConfig.time)
        const weekdays = simpleConfig.weekdays.join(',')
        cron = `${time.second()} ${time.minute()} ${time.hour()} * * ${weekdays}`
      }
      break

    case 'monthly':
      if (simpleConfig.time && simpleConfig.monthDay) {
        const time = dayjs(simpleConfig.time)
        const day = simpleConfig.monthDay === 'last' ? 'L' : simpleConfig.monthDay
        cron = `${time.second()} ${time.minute()} ${time.hour()} ${day} * *`
      }
      break

    case 'yearly':
      if (simpleConfig.time && simpleConfig.month && simpleConfig.monthDay) {
        const time = dayjs(simpleConfig.time)
        cron = `${time.second()} ${time.minute()} ${time.hour()} ${simpleConfig.monthDay} ${simpleConfig.month} *`
      }
      break
  }

  if (cron) {
    cronExpression.value = cron
    emit('update:modelValue', cron)
    validateCronExpression(cron)
  }

  isUpdating.value = false
}

// 处理手动输入的Cron表达式
const handleCronChange = (e) => {
  if (isUpdating.value || isModeChanging.value) return

  const value = e.target.value
  // 规范化cron表达式为6字段格式
  const normalizedCron = ensureSixFieldCronExpr(value)

  isUpdating.value = true
  cronExpression.value = normalizedCron

  // 在高级模式下尝试解析为简单模式（但不强制切换）
  if (mode.value === 'advanced') {
    parseCronToSimpleMode(normalizedCron)
  }

  emit('update:modelValue', normalizedCron)
  validateCronExpression(normalizedCron)
  isUpdating.value = false
}

// 验证Cron表达式
const validateCronExpression = async (cron) => {
  if (!cron) return

  try {
    // 调用后端API验证Cron表达式
    const response = await cronApi.validateCronExpr({ cron_expr: cron })

    if (response.code === 200 && response.data.valid) {
      cronDescription.value = response.data.description || '自定义时间执行'
      nextRuns.value = response.data.next_runs || []

      emit('validate', {
        valid: true,
        description: response.data.description,
        next_runs: response.data.next_runs
      })
    } else {
      cronDescription.value = ''
      nextRuns.value = []

      emit('validate', {
        valid: false,
        error: response.data.error || '表达式格式错误'
      })
    }
  } catch (error) {
    console.error('验证Cron表达式失败:', error)

    // 如果API调用失败，回退到本地验证
    const description = generateCronDescription(cron)
    cronDescription.value = description
    nextRuns.value = generateNextRuns(cron)

    emit('validate', {
      valid: true,
      description: description,
      next_runs: nextRuns.value
    })
  }
}

// 生成Cron表达式描述（简化版）
const generateCronDescription = (cron) => {
  const parts = cron.split(' ')
  if (parts.length !== 6) return '表达式格式错误'
  
  const [sec, min, hour, day, month, week] = parts
  
  if (cron === '0 0 8 * * *') return '每天上午8点执行'
  if (cron.includes('*/5') && cron.includes('* * * *')) return '每5分钟执行一次'
  if (cron.includes('0 0 * * *')) return '每小时执行一次'
  
  return '自定义时间执行'
}

// 确保cron表达式是6字段格式（秒 分 时 日 月 周）
const ensureSixFieldCronExpr = (cronExpr) => {
  // 清理输入：去除前后空格，压缩多个空格为单个空格
  cronExpr = cronExpr?.trim() || ''
  if (!cronExpr) {
    return '0 0 0 * * *' // 默认：每天午夜执行
  }

  // 使用正则表达式分割，处理多个空格的情况
  cronExpr = cronExpr.replace(/\s+/g, ' ')
  const fields = cronExpr.split(' ').filter(field => field.length > 0)

  switch (fields.length) {
    case 6:
      // 已经是6字段格式，验证每个字段的合法性
      return validateAndNormalizeSixFields(fields)
    case 5:
      // 5字段格式，在前面添加秒字段
      return validateAndNormalizeSixFields(['0', ...fields])
    case 4:
      // 4字段格式（分 时 日 月），添加秒和周字段
      return validateAndNormalizeSixFields(['0', ...fields, '*'])
    case 3:
      // 3字段格式（时 日 月），添加秒、分和周字段
      return validateAndNormalizeSixFields(['0', '0', fields[0], fields[1], fields[2], '*'])
    default:
      // 其他情况，返回默认表达式
      console.warn('无效的cron表达式字段数量:', cronExpr, '字段数:', fields.length)
      return '0 0 0 * * *' // 默认：每天午夜执行
  }
}

// 验证并规范化6字段cron表达式
const validateAndNormalizeSixFields = (fields) => {
  if (fields.length !== 6) {
    return '0 0 0 * * *'
  }

  // 验证每个字段的基本格式
  for (let i = 0; i < fields.length; i++) {
    if (!fields[i] || fields[i] === '') {
      fields[i] = '*'
    }
    // 基本的字符验证：只允许数字、*、/、-、,
    if (!/^[0-9*,/\-]+$/.test(fields[i])) {
      console.warn('cron字段包含非法字符:', fields[i], '位置:', i)
      fields[i] = '*'
    }
  }

  // 特殊处理：确保周字段的合法性（0-7，其中0和7都表示周日）
  const weekField = fields[5]
  if (weekField !== '*' && !/^[0-7,/\-]+$/.test(weekField)) {
    fields[5] = '*'
  }

  return fields.join(' ')
}

// 生成接下来的执行时间（模拟）
const generateNextRuns = (cron) => {
  // 这里应该使用专业的cron解析库，这里只是模拟
  const now = dayjs()
  const runs = []

  for (let i = 1; i <= 5; i++) {
    runs.push(now.add(i, 'hour').format('YYYY-MM-DD HH:mm:ss'))
  }

  return runs
}

// 解析现有Cron表达式到简单模式
const parseCronToSimpleMode = (cronExpr) => {
  if (!cronExpr || isUpdating.value) {
    return false
  }

  try {
    const parts = cronExpr.trim().split(/\s+/)
    if (parts.length !== 6) {
      return false
    }

    const [second, minute, hour, day, month, weekday] = parts

    // 检查是否是简单的每天模式 (S M H * * *)
    if (day === '*' && month === '*' && weekday === '*') {
      const hourNum = parseInt(hour)
      const minuteNum = parseInt(minute)
      const secondNum = parseInt(second)

      if (!isNaN(hourNum) && !isNaN(minuteNum) && !isNaN(secondNum) &&
          hourNum >= 0 && hourNum <= 23 && minuteNum >= 0 && minuteNum <= 59 && secondNum >= 0 && secondNum <= 59) {
        simpleConfig.type = 'daily'
        const timeStr = `${hourNum.toString().padStart(2, '0')}:${minuteNum.toString().padStart(2, '0')}:${secondNum.toString().padStart(2, '0')}`
        simpleConfig.time = dayjs(timeStr, 'HH:mm:ss')
        return true
      }
    }

    // 检查是否是简单的每周模式 (S M H * * W)
    if (day === '*' && month === '*' && /^\d+$/.test(weekday)) {
      const hourNum = parseInt(hour)
      const minuteNum = parseInt(minute)
      const secondNum = parseInt(second)
      const weekdayNum = parseInt(weekday)

      if (!isNaN(hourNum) && !isNaN(minuteNum) && !isNaN(secondNum) &&
          hourNum >= 0 && hourNum <= 23 && minuteNum >= 0 && minuteNum <= 59 &&
          secondNum >= 0 && secondNum <= 59 && weekdayNum >= 0 && weekdayNum <= 7) {
        simpleConfig.type = 'weekly'
        simpleConfig.time = dayjs(`${hourNum.toString().padStart(2, '0')}:${minuteNum.toString().padStart(2, '0')}:${secondNum.toString().padStart(2, '0')}`, 'HH:mm:ss')
        simpleConfig.weekdays = [weekdayNum === 0 ? 7 : weekdayNum] // 转换周日
        return true
      }
    }

    // 检查是否是简单的每月模式 (S M H D * *)
    if (month === '*' && weekday === '*' && /^\d+$/.test(day)) {
      const hourNum = parseInt(hour)
      const minuteNum = parseInt(minute)
      const secondNum = parseInt(second)
      const dayNum = parseInt(day)

      if (!isNaN(hourNum) && !isNaN(minuteNum) && !isNaN(secondNum) &&
          hourNum >= 0 && hourNum <= 23 && minuteNum >= 0 && minuteNum <= 59 &&
          secondNum >= 0 && secondNum <= 59 && dayNum >= 1 && dayNum <= 31) {
        simpleConfig.type = 'monthly'
        simpleConfig.time = dayjs(`${hourNum.toString().padStart(2, '0')}:${minuteNum.toString().padStart(2, '0')}:${secondNum.toString().padStart(2, '0')}`, 'HH:mm:ss')
        simpleConfig.monthDay = dayNum
        return true
      }
    }

    // 检查是否是间隔模式
    if (second.startsWith('*/') && minute === '*' && hour === '*' && day === '*' && month === '*' && weekday === '*') {
      const intervalValue = parseInt(second.substring(2))
      if (!isNaN(intervalValue) && intervalValue > 0) {
        simpleConfig.type = 'interval'
        simpleConfig.interval = { value: intervalValue, unit: 'seconds' }
        return true
      }
    }

    if (second === '0' && minute.startsWith('*/') && hour === '*' && day === '*' && month === '*' && weekday === '*') {
      const intervalValue = parseInt(minute.substring(2))
      if (!isNaN(intervalValue) && intervalValue > 0) {
        simpleConfig.type = 'interval'
        simpleConfig.interval = { value: intervalValue, unit: 'minutes' }
        return true
      }
    }

    if (second === '0' && minute === '0' && hour.startsWith('*/') && day === '*' && month === '*' && weekday === '*') {
      const intervalValue = parseInt(hour.substring(2))
      if (!isNaN(intervalValue) && intervalValue > 0) {
        simpleConfig.type = 'interval'
        simpleConfig.interval = { value: intervalValue, unit: 'hours' }
        return true
      }
    }
  } catch (error) {
    console.warn('Failed to parse cron expression:', error)
  }

  // 如果无法解析为简单模式，返回false但不强制切换模式
  return false
}

// 防止循环更新的标志
const isUpdating = ref(false)
const isModeChanging = ref(false)
// 存储高级模式的表达式，用于模式切换时恢复
const advancedModeExpression = ref('')

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== cronExpression.value && !isUpdating.value) {
    isUpdating.value = true
    cronExpression.value = newValue
    // 尝试解析为简单模式
    if (newValue && mode.value === 'simple') {
      parseCronToSimpleMode(newValue)
    }
    validateCronExpression(newValue)
    isUpdating.value = false
  }
})

// 监听高级模式文本框的实时变化，实现双向绑定
watch(cronExpression, (newValue) => {
  if (!isUpdating.value && !isModeChanging.value) {
    isUpdating.value = true
    // 只有在高级模式下才尝试解析为简单模式（避免覆盖用户输入）
    if (newValue && newValue.trim() && mode.value === 'advanced') {
      // 保存高级模式的表达式
      advancedModeExpression.value = newValue
      parseCronToSimpleMode(newValue)
    }
    validateCronExpression(newValue)
    // 通知父组件值已改变
    emit('update:modelValue', newValue)
    isUpdating.value = false
  }
})

// 监听简单模式配置变化，自动生成Cron表达式
watch(simpleConfig, (newConfig) => {
  console.log(`🔧 简单模式配置变化:`, newConfig)
  if (mode.value === 'simple' && !isUpdating.value && !isModeChanging.value) {
    isUpdating.value = true
    console.log(`🚀 触发简单模式表达式生成`)
    updateCronExpression()
    isUpdating.value = false
  }
}, { deep: true })

// 监听模式切换，当切换到简单模式时尝试解析当前表达式
watch(mode, (newMode, oldMode) => {
  console.log(`🔄 模式切换: ${oldMode} -> ${newMode} 当前表达式: ${cronExpression.value} 保存的表达式: ${advancedModeExpression.value}`)

  if (newMode === 'simple' && oldMode === 'advanced' && !isUpdating.value) {
    isModeChanging.value = true

    // 使用保存的高级模式表达式，如果没有则使用当前表达式
    const expressionToUse = advancedModeExpression.value || cronExpression.value
    console.log(`🔍 尝试解析表达式到简单模式: ${expressionToUse}`)

    if (expressionToUse && expressionToUse.trim()) {
      // 先恢复正确的表达式值
      cronExpression.value = expressionToUse

      // 尝试解析当前的Cron表达式到简单模式
      const parseSuccess = parseCronToSimpleMode(expressionToUse)
      console.log(`📊 解析结果: ${parseSuccess}`)

      if (parseSuccess) {
        // 解析成功后，确保表达式预览也更新为正确的值
        nextTick(() => {
          console.log(`🔄 解析成功，更新表达式预览为: ${expressionToUse}`)
          cronExpression.value = expressionToUse
          updateCronExpression()
        })
      } else {
        // 如果解析失败，切换回高级模式
        nextTick(() => {
          mode.value = 'advanced'
          cronExpression.value = expressionToUse
        })
      }
    }
    isModeChanging.value = false
  } else if (newMode === 'simple' && !isUpdating.value) {
    // 切换到简单模式时，确保生成表达式
    console.log(`🔄 切换到简单模式，生成表达式`)
    nextTick(() => {
      updateCronExpression()
    })
  }
})

// 组件挂载时初始化
onMounted(() => {
  console.log(`🚀 CronBuilder组件挂载，初始值: ${cronExpression.value}`)
  if (cronExpression.value && cronExpression.value.trim()) {
    // 如果有初始值，尝试解析为简单模式
    console.log(`📝 有初始值，尝试解析: ${cronExpression.value}`)
    parseCronToSimpleMode(cronExpression.value)
    validateCronExpression(cronExpression.value)
  } else {
    // 如果没有初始值，生成默认表达式
    console.log(`🔧 没有初始值，生成默认表达式`)
    updateCronExpression()
  }
})
</script>

<style scoped lang="scss">
.cron-builder {
  .mode-switch {
    margin-bottom: 16px;
  }

  .simple-mode, .advanced-mode {
    margin-bottom: 16px;
  }

  .cron-help {
    margin-top: 4px;
  }

  .cron-preview {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 6px;
    margin-top: 16px;

    .cron-description {
      margin-top: 8px;
    }

    .next-runs {
      margin-top: 12px;

      ul {
        margin: 8px 0 0 16px;
        
        li {
          margin-bottom: 4px;
          color: #666;
        }
      }
    }
  }
}
</style>
