<template>
  <div class="cron-history">
    <div class="page-header">
      <h2>执行历史</h2>
      <a-button @click="refreshData" :loading="loading">
        <template #icon>
          <ReloadOutlined />
        </template>
        刷新
      </a-button>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filters.taskId"
            placeholder="选择任务"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="task in taskList"
              :key="task.id"
              :value="task.id"
            >
              {{ task.name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.clientId"
            placeholder="选择客户端"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="client in clientList"
              :key="client.id"
              :value="client.id"
            >
              {{ client.hostname || client.remoteAddr }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.status"
            placeholder="执行状态"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="success">成功</a-select-option>
            <a-select-option value="failed">失败</a-select-option>
            <a-select-option value="timeout">超时</a-select-option>
            <a-select-option value="cancelled">取消</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-range-picker
            v-model:value="dateRange"
            @change="handleFilterChange"
            style="width: 100%"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 执行记录列表 -->
    <a-table
      :columns="columns"
      :data-source="executionList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 执行状态 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ record.status_display }}
        </a-tag>
      </template>

      <!-- 执行时长 -->
      <template #duration="{ record }">
        <span>{{ formatDuration(record.duration) }}</span>
      </template>

      <!-- 操作 -->
      <template #action="{ record }">
        <a-space>
          <a-button size="small" @click="viewDetails(record)">
            查看详情
          </a-button>
          <a-button
            v-if="record.output"
            size="small"
            @click="viewOutput(record)"
          >
            查看输出
          </a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 详情模态框 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="执行详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentExecution">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务名称">
            {{ currentExecution.task_name }}
          </a-descriptions-item>
          <a-descriptions-item label="客户端">
            {{ currentExecution.client_name }}
          </a-descriptions-item>
          <a-descriptions-item label="执行状态">
            <a-tag :color="getStatusColor(currentExecution.status)">
              {{ currentExecution.status_display }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="执行时长">
            {{ formatDuration(currentExecution.duration) }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ currentExecution.start_time }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ currentExecution.end_time }}
          </a-descriptions-item>
        </a-descriptions>

        <div v-if="currentExecution.output" style="margin-top: 16px">
          <h4>执行输出:</h4>
          <pre class="output-content">{{ currentExecution.output }}</pre>
        </div>

        <div v-if="currentExecution.error_msg" style="margin-top: 16px">
          <h4>错误信息:</h4>
          <pre class="error-content">{{ currentExecution.error_msg }}</pre>
        </div>
      </div>
    </a-modal>

    <!-- 输出模态框 -->
    <a-modal
      v-model:visible="outputModalVisible"
      title="执行输出"
      width="800px"
      :footer="null"
    >
      <div v-if="currentExecution">
        <div style="margin-bottom: 16px">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="任务名称">
              {{ currentExecution.task_name }}
            </a-descriptions-item>
            <a-descriptions-item label="执行状态">
              <a-tag :color="getStatusColor(currentExecution.status)">
                {{ currentExecution.status_display }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="执行时间">
              {{ currentExecution.start_time }}
            </a-descriptions-item>
            <a-descriptions-item label="执行时长">
              {{ formatDuration(currentExecution.duration) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div v-if="currentExecution.output">
          <h4>执行输出:</h4>
          <pre class="output-content">{{ currentExecution.output }}</pre>
        </div>

        <div v-if="currentExecution.error_msg" style="margin-top: 16px">
          <h4>错误信息:</h4>
          <pre class="error-content">{{ currentExecution.error_msg }}</pre>
        </div>

        <div v-if="!currentExecution.output && !currentExecution.error_msg" style="text-align: center; padding: 20px; color: #999;">
          暂无输出内容
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import * as cronApi from '@/api/cron'
import * as clientApi from '@/api/client'

// 响应式数据
const loading = ref(false)
const executionList = ref([])
const taskList = ref([])
const clientList = ref([])
const detailModalVisible = ref(false)
const outputModalVisible = ref(false)
const currentExecution = ref(null)
const dateRange = ref([])

// 筛选条件
const filters = reactive({
  taskId: undefined,
  clientId: undefined,
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80
  },
  {
    title: '任务名称',
    dataIndex: 'task_name',
    width: 150
  },
  {
    title: '客户端',
    dataIndex: 'client_name',
    width: 150
  },
  {
    title: '执行状态',
    dataIndex: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '开始时间',
    dataIndex: 'start_time',
    width: 150
  },
  {
    title: '结束时间',
    dataIndex: 'end_time',
    width: 150
  },
  {
    title: '执行时长',
    dataIndex: 'duration',
    width: 100,
    slots: { customRender: 'duration' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 获取执行记录列表
const fetchExecutionList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...filters
    }

    // 添加日期范围筛选
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0].format('YYYY-MM-DD')
      params.end_date = dateRange.value[1].format('YYYY-MM-DD')
    }
    
    const response = await cronApi.getExecutionList(params)
    if (response.code === 200) {
      executionList.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取执行记录失败:', error)
    message.error('获取执行记录失败')
  } finally {
    loading.value = false
  }
}

// 获取任务列表
const fetchTaskList = async () => {
  try {
    const response = await cronApi.getTaskList({ page: 1, page_size: 1000 })
    if (response.code === 200) {
      taskList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
  }
}

// 获取客户端列表
const fetchClientList = async () => {
  try {
    const response = await clientApi.getClientList({ page: 1, page_size: 1000 })
    if (response.code === 200) {
      clientList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取客户端列表失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    fetchTaskList(),
    fetchClientList(),
    fetchExecutionList()
  ])
  message.success('数据刷新成功')
}

// 处理筛选条件变化
const handleFilterChange = () => {
  pagination.current = 1
  fetchExecutionList()
}

// 处理表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchExecutionList()
}

// 查看详情
const viewDetails = (execution) => {
  currentExecution.value = execution
  detailModalVisible.value = true
}

// 查看输出
const viewOutput = (execution) => {
  currentExecution.value = execution
  outputModalVisible.value = true
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    success: 'green',
    failed: 'red',
    timeout: 'orange',
    cancelled: 'default'
  }
  return colors[status] || 'default'
}

// 格式化执行时长
const formatDuration = (duration) => {
  if (!duration) return '-'
  
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTaskList()
  fetchClientList()
  fetchExecutionList()
})
</script>

<style scoped lang="scss">
.cron-history {
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
    }
  }

  .filter-section {
    margin-bottom: 16px;
  }

  .output-content {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    white-space: pre-wrap;
  }

  .error-content {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    padding: 12px;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #ff4d4f;
    white-space: pre-wrap;
  }
}
</style>
