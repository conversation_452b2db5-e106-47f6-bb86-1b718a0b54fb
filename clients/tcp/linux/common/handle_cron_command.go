//go:build linux
// +build linux

package common

import (
	"context"
	"log"
	"os/exec"
	"strings"
	"time"
)

// CronCommandRequest 定时任务命令请求结构
type CronCommandRequest struct {
	TaskID    uint64 `json:"task_id"`
	Command   string `json:"command"`
	Timeout   int    `json:"timeout"`   // 超时时间（秒）
	Directory string `json:"directory"` // 工作目录
}

// CronCommandResponse 定时任务命令响应结构
type CronCommandResponse struct {
	TaskID    uint64 `json:"task_id"`
	Success   bool   `json:"success"`
	Output    string `json:"output"`
	Error     string `json:"error"`
	ExitCode  int    `json:"exit_code"`
	Duration  int64  `json:"duration"` // 执行时长（毫秒）
}

// handleCronCommand 处理定时任务命令执行
func (cm *ConnectionManager) handleCronCommand(data []byte) {
	var request CronCommandRequest
	if err := cm.serializer.Deserialize(data, &request); err != nil {
		log.Printf("❌ 解析定时任务命令请求失败: %v", err)
		cm.sendCronCommandError(0, "解析请求失败", err.Error())
		return
	}

	log.Printf("🚀 开始执行定时任务命令: TaskID=%d, Command=%s", request.TaskID, request.Command)

	// 异步执行命令，避免阻塞
	go cm.executeCronCommand(request)
}

// executeCronCommand 异步执行定时任务命令
func (cm *ConnectionManager) executeCronCommand(request CronCommandRequest) {
	startTime := time.Now()

	// 设置超时时间，默认5分钟
	timeout := time.Duration(request.Timeout) * time.Second
	if timeout <= 0 {
		timeout = 5 * time.Minute
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 解析命令和参数
	cmdParts := strings.Fields(request.Command)
	if len(cmdParts) == 0 {
		cm.sendCronCommandError(request.TaskID, "命令为空", "")
		return
	}

	// 创建命令
	cmd := exec.CommandContext(ctx, cmdParts[0], cmdParts[1:]...)

	// 设置工作目录
	if request.Directory != "" {
		cmd.Dir = request.Directory
	}

	// 执行命令并获取输出
	output, err := cmd.CombinedOutput()
	duration := time.Since(startTime).Milliseconds()

	// 构建响应
	response := CronCommandResponse{
		TaskID:   request.TaskID,
		Success:  err == nil,
		Output:   string(output),
		Duration: duration,
	}

	// 处理错误和退出码
	if err != nil {
		response.Error = err.Error()
		if exitError, ok := err.(*exec.ExitError); ok {
			response.ExitCode = exitError.ExitCode()
		} else {
			response.ExitCode = -1
		}
		log.Printf("❌ 定时任务命令执行失败: TaskID=%d, Error=%s", request.TaskID, err.Error())
	} else {
		response.ExitCode = 0
		log.Printf("✅ 定时任务命令执行成功: TaskID=%d, Duration=%dms", request.TaskID, duration)
	}

	// 发送响应
	cm.sendCronCommandResponse(response)
}

// sendCronCommandResponse 发送定时任务命令响应
func (cm *ConnectionManager) sendCronCommandResponse(response CronCommandResponse) {
	// 序列化响应
	responseData, err := cm.serializer.Serialize(response)
	if err != nil {
		log.Printf("❌ 序列化定时任务命令响应失败: %v", err)
		return
	}

	// 创建TLV包
	packets, err := cm.CreatePackets(responseData, RunCommand, ExecCronCommandOutput)
	if err != nil {
		log.Printf("❌ 创建定时任务命令响应包失败: %v", err)
		return
	}

	// 发送包
	for _, packet := range packets {
		packetBytes := packet.Serialize()
		if _, err := cm.conn.Write(packetBytes); err != nil {
			log.Printf("❌ 发送定时任务命令响应失败: %v", err)
			return
		}
	}

	log.Printf("📤 定时任务命令响应发送成功: TaskID=%d", response.TaskID)
}

// sendCronCommandError 发送定时任务命令错误响应
func (cm *ConnectionManager) sendCronCommandError(taskID uint64, message string, detail string) {
	response := CronCommandResponse{
		TaskID:   taskID,
		Success:  false,
		Error:    message,
		Output:   detail,
		ExitCode: -1,
		Duration: 0,
	}

	cm.sendCronCommandResponse(response)
}
