//go:build windows
// +build windows

package common

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// CronScriptRequest 定时任务脚本请求结构
type CronScriptRequest struct {
	TaskID      uint64 `json:"task_id"`
	ScriptType  string `json:"script_type"`  // bat, ps1, cmd, py
	ScriptContent string `json:"script_content"` // 脚本内容
	Timeout     int    `json:"timeout"`      // 超时时间（秒）
	Directory   string `json:"directory"`    // 工作目录
	Arguments   string `json:"arguments"`    // 脚本参数（可选）
}

// CronScriptResponse 定时任务脚本响应结构
type CronScriptResponse struct {
	TaskID    uint64 `json:"task_id"`
	Success   bool   `json:"success"`
	Output    string `json:"output"`
	Error     string `json:"error"`
	ExitCode  int    `json:"exit_code"`
	Duration  int64  `json:"duration"` // 执行时长（毫秒）
}

// handleCronScript 处理定时任务脚本执行
func (cm *ConnectionManager) handleCronScript(data []byte) {
	var request CronScriptRequest
	if err := cm.serializer.Deserialize(data, &request); err != nil {
		log.Printf("❌ 解析定时任务脚本请求失败: %v", err)
		cm.sendCronScriptError(0, "解析请求失败", err.Error())
		return
	}

	log.Printf("🚀 开始执行定时任务脚本: TaskID=%d, Type=%s", request.TaskID, request.ScriptType)

	// 异步执行脚本，避免阻塞
	go cm.executeCronScript(request)
}

// executeCronScript 异步执行定时任务脚本
func (cm *ConnectionManager) executeCronScript(request CronScriptRequest) {
	startTime := time.Now()

	// 设置超时时间，默认5分钟
	timeout := time.Duration(request.Timeout) * time.Second
	if timeout <= 0 {
		timeout = 5 * time.Minute
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 创建临时脚本文件
	tempFile, err := cm.createTempScript(request.ScriptType, request.ScriptContent)
	if err != nil {
		cm.sendCronScriptError(request.TaskID, "创建临时脚本失败", err.Error())
		return
	}
	defer os.Remove(tempFile) // 确保清理临时文件

	// 根据脚本类型执行
	var cmd *exec.Cmd
	switch strings.ToLower(request.ScriptType) {
	case "bat", "cmd":
		// 批处理文件
		cmd = exec.CommandContext(ctx, "cmd", "/c", tempFile)
	case "ps1":
		// PowerShell脚本
		args := []string{"-ExecutionPolicy", "Bypass", "-File", tempFile}
		if request.Arguments != "" {
			args = append(args, strings.Fields(request.Arguments)...)
		}
		cmd = exec.CommandContext(ctx, "powershell", args...)
	case "py":
		// Python脚本
		args := []string{tempFile}
		if request.Arguments != "" {
			args = append(args, strings.Fields(request.Arguments)...)
		}
		cmd = exec.CommandContext(ctx, "python", args...)
	default:
		cm.sendCronScriptError(request.TaskID, "不支持的脚本类型", fmt.Sprintf("脚本类型 %s 不受支持", request.ScriptType))
		return
	}

	// 设置工作目录
	if request.Directory != "" {
		cmd.Dir = request.Directory
	}

	// 执行脚本并获取输出
	output, err := cmd.CombinedOutput()
	duration := time.Since(startTime).Milliseconds()

	// 构建响应
	response := CronScriptResponse{
		TaskID:   request.TaskID,
		Success:  err == nil,
		Output:   string(output),
		Duration: duration,
	}

	// 处理错误和退出码
	if err != nil {
		response.Error = err.Error()
		if exitError, ok := err.(*exec.ExitError); ok {
			response.ExitCode = exitError.ExitCode()
		} else {
			response.ExitCode = -1
		}
		log.Printf("❌ 定时任务脚本执行失败: TaskID=%d, Type=%s, Error=%s", request.TaskID, request.ScriptType, err.Error())
	} else {
		response.ExitCode = 0
		log.Printf("✅ 定时任务脚本执行成功: TaskID=%d, Type=%s, Duration=%dms", request.TaskID, request.ScriptType, duration)
	}

	// 发送响应
	cm.sendCronScriptResponse(response)
}

// createTempScript 创建临时脚本文件
func (cm *ConnectionManager) createTempScript(scriptType, content string) (string, error) {
	// 获取临时目录
	tempDir := os.TempDir()
	
	// 根据脚本类型确定文件扩展名
	var ext string
	switch strings.ToLower(scriptType) {
	case "bat", "cmd":
		ext = ".bat"
	case "ps1":
		ext = ".ps1"
	case "py":
		ext = ".py"
	default:
		ext = ".txt"
	}

	// 创建临时文件
	tempFile, err := ioutil.TempFile(tempDir, fmt.Sprintf("cron_script_*%s", ext))
	if err != nil {
		return "", fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer tempFile.Close()

	// 写入脚本内容
	if _, err := tempFile.WriteString(content); err != nil {
		os.Remove(tempFile.Name())
		return "", fmt.Errorf("写入脚本内容失败: %w", err)
	}

	return tempFile.Name(), nil
}

// sendCronScriptResponse 发送定时任务脚本响应
func (cm *ConnectionManager) sendCronScriptResponse(response CronScriptResponse) {
	// 序列化响应
	responseData, err := cm.serializer.Serialize(response)
	if err != nil {
		log.Printf("❌ 序列化定时任务脚本响应失败: %v", err)
		return
	}

	// 创建TLV包
	packets, err := cm.CreatePackets(responseData, RunCommand, ExecCronScriptOutput)
	if err != nil {
		log.Printf("❌ 创建定时任务脚本响应包失败: %v", err)
		return
	}

	// 发送包
	for _, packet := range packets {
		packetBytes := packet.Serialize()
		if _, err := cm.conn.Write(packetBytes); err != nil {
			log.Printf("❌ 发送定时任务脚本响应失败: %v", err)
			return
		}
	}

	log.Printf("📤 定时任务脚本响应发送成功: TaskID=%d", response.TaskID)
}

// sendCronScriptError 发送定时任务脚本错误响应
func (cm *ConnectionManager) sendCronScriptError(taskID uint64, message string, detail string) {
	response := CronScriptResponse{
		TaskID:   taskID,
		Success:  false,
		Error:    message,
		Output:   detail,
		ExitCode: -1,
		Duration: 0,
	}

	cm.sendCronScriptResponse(response)
}
